!function(e){"use strict";function r(e){return function(t){if(this===t.target){return e.apply(this,arguments)}}}var t=function(e,t){this.init(e,t)};t.prototype={constructor:t,init:function(t,n){this.$element=e(t);this.options=e.extend({},e.fn.modalmanager.defaults,this.$element.data(),typeof n=="object"&&n);this.stack=[];this.backdropCount=0;if(this.options.resize){var r,i=this;e(window).on("resize.modal",function(){r&&clearTimeout(r);r=setTimeout(function(){for(var e=0;e<i.stack.length;e++){i.stack[e].isShown&&i.stack[e].layout()}},10)})}},createModal:function(t,n){e(t).modal(e.extend({manager:this},n))},appendModal:function(t){this.stack.push(t);var n=this;t.$element.on("show.modalmanager",r(function(r){var i=function(){t.isShown=true;var r=e.support.transition&&t.$element.hasClass("fade");n.$element.toggleClass("modal-open",n.hasOpenModal()).toggleClass("page-overflow",e(window).height()<n.$element.height());t.$parent=t.$element.parent();t.$container=n.createContainer(t);t.$element.appendTo(t.$container);n.backdrop(t,function(){t.$element.show();if(r){t.$element[0].offsetWidth}t.layout();t.$element.addClass("in").attr("aria-hidden",false);var i=function(){n.setFocus();t.$element.trigger("shown")};r?t.$element.one(e.support.transition.end,i):i()})};t.options.replace?n.replace(i):i()}));t.$element.on("hidden.modalmanager",r(function(r){n.backdrop(t);if(t.$backdrop){e.support.transition&&t.$element.hasClass("fade")?t.$backdrop.one(e.support.transition.end,function(){n.destroyModal(t)}):n.destroyModal(t)}else{n.destroyModal(t)}}));t.$element.on("destroy.modalmanager",r(function(e){n.removeModal(t)}))},destroyModal:function(e){e.destroy();var t=this.hasOpenModal();this.$element.toggleClass("modal-open",t);if(!t){this.$element.removeClass("page-overflow")}this.removeContainer(e);this.setFocus()},hasOpenModal:function(){for(var e=0;e<this.stack.length;e++){if(this.stack[e].isShown)return true}return false},setFocus:function(){var e;for(var t=0;t<this.stack.length;t++){if(this.stack[t].isShown)e=this.stack[t]}if(!e)return;e.focus()},removeModal:function(e){e.$element.off(".modalmanager");if(e.$backdrop)this.removeBackdrop(e);this.stack.splice(this.getIndexOfModal(e),1)},getModalAt:function(e){return this.stack[e]},getIndexOfModal:function(e){for(var t=0;t<this.stack.length;t++){if(e===this.stack[t])return t}},replace:function(t){var n;for(var i=0;i<this.stack.length;i++){if(this.stack[i].isShown)n=this.stack[i]}if(n){this.$backdropHandle=n.$backdrop;n.$backdrop=null;t&&n.$element.one("hidden",r(e.proxy(t,this)));n.hide()}else if(t){t()}},removeBackdrop:function(e){e.$backdrop.remove();e.$backdrop=null},createBackdrop:function(t){var n;if(!this.$backdropHandle){n=e('<div class="modal-backdrop '+t+'" />').appendTo(this.$element)}else{n=this.$backdropHandle;n.off(".modalmanager");this.$backdropHandle=null;this.isLoading&&this.removeSpinner()}return n},removeContainer:function(e){e.$container.remove();e.$container=null},createContainer:function(t){var i;i=e('<div class="modal-scrollable">').css("z-index",n("modal",t?this.getIndexOfModal(t):this.stack.length)).appendTo(this.$element);if(t&&t.options.backdrop!="static"){i.on("click.modal",r(function(e){t.hide()}))}else if(t){i.on("click.modal",r(function(e){t.attention()}))}return i},backdrop:function(t,r){var i=t.$element.hasClass("fade")?"fade":"",s=t.options.backdrop&&this.backdropCount<this.options.backdropLimit;if(t.isShown&&s){var o=e.support.transition&&i&&!this.$backdropHandle;t.$backdrop=this.createBackdrop(i);t.$backdrop.css("z-index",n("backdrop",this.getIndexOfModal(t)));if(o)t.$backdrop[0].offsetWidth;t.$backdrop.addClass("in");this.backdropCount+=1;o?t.$backdrop.one(e.support.transition.end,r):r()}else if(!t.isShown&&t.$backdrop){t.$backdrop.removeClass("in");this.backdropCount-=1;var u=this;e.support.transition&&t.$element.hasClass("fade")?t.$backdrop.one(e.support.transition.end,function(){u.removeBackdrop(t)}):u.removeBackdrop(t)}else if(r){r()}},removeSpinner:function(){this.$spinner&&this.$spinner.remove();this.$spinner=null;this.isLoading=false},removeLoading:function(){this.$backdropHandle&&this.$backdropHandle.remove();this.$backdropHandle=null;this.removeSpinner()},loading:function(t){t=t||function(){};this.$element.toggleClass("modal-open",!this.isLoading||this.hasOpenModal()).toggleClass("page-overflow",e(window).height()<this.$element.height());if(!this.isLoading){this.$backdropHandle=this.createBackdrop("fade");this.$backdropHandle[0].offsetWidth;this.$backdropHandle.css("z-index",n("backdrop",this.stack.length)).addClass("in");var r=e(this.options.spinner).css("z-index",n("modal",this.stack.length)).appendTo(this.$element).addClass("in");this.$spinner=e(this.createContainer()).append(r).on("click.modalmanager",e.proxy(this.loading,this));this.isLoading=true;e.support.transition?this.$backdropHandle.one(e.support.transition.end,t):t()}else if(this.isLoading&&this.$backdropHandle){this.$backdropHandle.removeClass("in");var i=this;e.support.transition?this.$backdropHandle.one(e.support.transition.end,function(){i.removeLoading()}):i.removeLoading()}else if(t){t(this.isLoading)}}};var n=function(){var t,n={};return function(r,i){if(typeof t==="undefined"){var s=e('<div class="modal hide" />').appendTo("body"),o=e('<div class="modal-backdrop hide" />').appendTo("body");n["modal"]=+s.css("z-index");n["backdrop"]=+o.css("z-index");t=n["modal"]-n["backdrop"];s.remove();o.remove();o=s=null}return n[r]+t*i}}();e.fn.modalmanager=function(n,r){return this.each(function(){var i=e(this),s=i.data("modalmanager");if(!s)i.data("modalmanager",s=new t(this,n));if(typeof n==="string")s[n].apply(s,[].concat(r))})};e.fn.modalmanager.defaults={backdropLimit:999,resize:true,spinner:'<div class="loading-spinner fade" style="width: 200px; margin-left: -100px;"><div class="progress progress-striped active"><div class="bar" style="width: 100%;"></div></div></div>'};e.fn.modalmanager.Constructor=t}(jQuery)