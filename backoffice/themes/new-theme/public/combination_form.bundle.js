(()=>{var e={4431:function(e,t,i){var r;!function(n){"use strict";var o,s=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,c=Math.ceil,a=Math.floor,l="[BigNumber Error] ",u=l+"Number primitive has more than 15 significant digits: ",p=1e14,f=14,h=9007199254740991,d=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],m=1e7,g=1e9;function b(e){var t=0|e;return e>0||e===t?t:t-1}function w(e){for(var t,i,r=1,n=e.length,o=e[0]+"";r<n;){for(t=e[r++]+"",i=f-t.length;i--;t="0"+t);o+=t}for(n=o.length;48===o.charCodeAt(--n););return o.slice(0,n+1||1)}function y(e,t){var i,r,n=e.c,o=t.c,s=e.s,c=t.s,a=e.e,l=t.e;if(!s||!c)return null;if(i=n&&!n[0],r=o&&!o[0],i||r)return i?r?0:-c:s;if(s!=c)return s;if(i=s<0,r=a==l,!n||!o)return r?0:!n^i?1:-1;if(!r)return a>l^i?1:-1;for(c=(a=n.length)<(l=o.length)?a:l,s=0;s<c;s++)if(n[s]!=o[s])return n[s]>o[s]^i?1:-1;return a==l?0:a>l^i?1:-1}function v(e,t,i,r){if(e<t||e>i||e!==a(e))throw Error(l+(r||"Argument")+("number"==typeof e?e<t||e>i?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function x(e){var t=e.c.length-1;return b(e.e/f)==t&&e.c[t]%2!=0}function S(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function _(e,t,i){var r,n;if(t<0){for(n=i+".";++t;n+=i);e=n+e}else if(++t>(r=e.length)){for(n=i,t-=r;--t;n+=i);e+=n}else t<r&&(e=e.slice(0,t)+"."+e.slice(t));return e}o=function e(t){var i,r,n,o,I,E,N,P,O,T,C=Z.prototype={constructor:Z,toString:null,valueOf:null},A=new Z(1),M=20,F=4,R=-7,D=21,B=-1e7,$=1e7,k=!1,j=1,U=0,G={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},L="0123456789abcdefghijklmnopqrstuvwxyz",z=!0;function Z(e,t){var i,o,c,l,p,d,m,g,b=this;if(!(b instanceof Z))return new Z(e,t);if(null==t){if(e&&!0===e._isBigNumber)return b.s=e.s,void(!e.c||e.e>$?b.c=b.e=null:e.e<B?b.c=[b.e=0]:(b.e=e.e,b.c=e.c.slice()));if((d="number"==typeof e)&&0*e==0){if(b.s=1/e<0?(e=-e,-1):1,e===~~e){for(l=0,p=e;p>=10;p/=10,l++);return void(l>$?b.c=b.e=null:(b.e=l,b.c=[e]))}g=String(e)}else{if(!s.test(g=String(e)))return n(b,g,d);b.s=45==g.charCodeAt(0)?(g=g.slice(1),-1):1}(l=g.indexOf("."))>-1&&(g=g.replace(".","")),(p=g.search(/e/i))>0?(l<0&&(l=p),l+=+g.slice(p+1),g=g.substring(0,p)):l<0&&(l=g.length)}else{if(v(t,2,L.length,"Base"),10==t&&z)return K(b=new Z(e),M+b.e+1,F);if(g=String(e),d="number"==typeof e){if(0*e!=0)return n(b,g,d,t);if(b.s=1/e<0?(g=g.slice(1),-1):1,Z.DEBUG&&g.replace(/^0\.0*|\./,"").length>15)throw Error(u+e)}else b.s=45===g.charCodeAt(0)?(g=g.slice(1),-1):1;for(i=L.slice(0,t),l=p=0,m=g.length;p<m;p++)if(i.indexOf(o=g.charAt(p))<0){if("."==o){if(p>l){l=m;continue}}else if(!c&&(g==g.toUpperCase()&&(g=g.toLowerCase())||g==g.toLowerCase()&&(g=g.toUpperCase()))){c=!0,p=-1,l=0;continue}return n(b,String(e),d,t)}d=!1,(l=(g=r(g,t,10,b.s)).indexOf("."))>-1?g=g.replace(".",""):l=g.length}for(p=0;48===g.charCodeAt(p);p++);for(m=g.length;48===g.charCodeAt(--m););if(g=g.slice(p,++m)){if(m-=p,d&&Z.DEBUG&&m>15&&(e>h||e!==a(e)))throw Error(u+b.s*e);if((l=l-p-1)>$)b.c=b.e=null;else if(l<B)b.c=[b.e=0];else{if(b.e=l,b.c=[],p=(l+1)%f,l<0&&(p+=f),p<m){for(p&&b.c.push(+g.slice(0,p)),m-=f;p<m;)b.c.push(+g.slice(p,p+=f));p=f-(g=g.slice(p)).length}else p-=m;for(;p--;g+="0");b.c.push(+g)}}else b.c=[b.e=0]}function q(e,t,i,r){var n,o,s,c,a;if(null==i?i=F:v(i,0,8),!e.c)return e.toString();if(n=e.c[0],s=e.e,null==t)a=w(e.c),a=1==r||2==r&&(s<=R||s>=D)?S(a,s):_(a,s,"0");else if(o=(e=K(new Z(e),t,i)).e,c=(a=w(e.c)).length,1==r||2==r&&(t<=o||o<=R)){for(;c<t;a+="0",c++);a=S(a,o)}else if(t-=s,a=_(a,o,"0"),o+1>c){if(--t>0)for(a+=".";t--;a+="0");}else if((t+=o-c)>0)for(o+1==c&&(a+=".");t--;a+="0");return e.s<0&&n?"-"+a:a}function V(e,t){for(var i,r=1,n=new Z(e[0]);r<e.length;r++){if(!(i=new Z(e[r])).s){n=i;break}t.call(n,i)&&(n=i)}return n}function H(e,t,i){for(var r=1,n=t.length;!t[--n];t.pop());for(n=t[0];n>=10;n/=10,r++);return(i=r+i*f-1)>$?e.c=e.e=null:i<B?e.c=[e.e=0]:(e.e=i,e.c=t),e}function K(e,t,i,r){var n,o,s,l,u,h,m,g=e.c,b=d;if(g){e:{for(n=1,l=g[0];l>=10;l/=10,n++);if((o=t-n)<0)o+=f,s=t,m=(u=g[h=0])/b[n-s-1]%10|0;else if((h=c((o+1)/f))>=g.length){if(!r)break e;for(;g.length<=h;g.push(0));u=m=0,n=1,s=(o%=f)-f+1}else{for(u=l=g[h],n=1;l>=10;l/=10,n++);m=(s=(o%=f)-f+n)<0?0:u/b[n-s-1]%10|0}if(r=r||t<0||null!=g[h+1]||(s<0?u:u%b[n-s-1]),r=i<4?(m||r)&&(0==i||i==(e.s<0?3:2)):m>5||5==m&&(4==i||r||6==i&&(o>0?s>0?u/b[n-s]:0:g[h-1])%10&1||i==(e.s<0?8:7)),t<1||!g[0])return g.length=0,r?(t-=e.e+1,g[0]=b[(f-t%f)%f],e.e=-t||0):g[0]=e.e=0,e;if(0==o?(g.length=h,l=1,h--):(g.length=h+1,l=b[f-o],g[h]=s>0?a(u/b[n-s]%b[s])*l:0),r)for(;;){if(0==h){for(o=1,s=g[0];s>=10;s/=10,o++);for(s=g[0]+=l,l=1;s>=10;s/=10,l++);o!=l&&(e.e++,g[0]==p&&(g[0]=1));break}if(g[h]+=l,g[h]!=p)break;g[h--]=0,l=1}for(o=g.length;0===g[--o];g.pop());}e.e>$?e.c=e.e=null:e.e<B&&(e.c=[e.e=0])}return e}function W(e){var t,i=e.e;return null===i?e.toString():(t=w(e.c),t=i<=R||i>=D?S(t,i):_(t,i,"0"),e.s<0?"-"+t:t)}return Z.clone=e,Z.ROUND_UP=0,Z.ROUND_DOWN=1,Z.ROUND_CEIL=2,Z.ROUND_FLOOR=3,Z.ROUND_HALF_UP=4,Z.ROUND_HALF_DOWN=5,Z.ROUND_HALF_EVEN=6,Z.ROUND_HALF_CEIL=7,Z.ROUND_HALF_FLOOR=8,Z.EUCLID=9,Z.config=Z.set=function(e){var t,i;if(null!=e){if("object"!=typeof e)throw Error(l+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(v(i=e[t],0,g,t),M=i),e.hasOwnProperty(t="ROUNDING_MODE")&&(v(i=e[t],0,8,t),F=i),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((i=e[t])&&i.pop?(v(i[0],-g,0,t),v(i[1],0,g,t),R=i[0],D=i[1]):(v(i,-g,g,t),R=-(D=i<0?-i:i))),e.hasOwnProperty(t="RANGE"))if((i=e[t])&&i.pop)v(i[0],-g,-1,t),v(i[1],1,g,t),B=i[0],$=i[1];else{if(v(i,-g,g,t),!i)throw Error(l+t+" cannot be zero: "+i);B=-($=i<0?-i:i)}if(e.hasOwnProperty(t="CRYPTO")){if((i=e[t])!==!!i)throw Error(l+t+" not true or false: "+i);if(i){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw k=!i,Error(l+"crypto unavailable");k=i}else k=i}if(e.hasOwnProperty(t="MODULO_MODE")&&(v(i=e[t],0,9,t),j=i),e.hasOwnProperty(t="POW_PRECISION")&&(v(i=e[t],0,g,t),U=i),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(i=e[t]))throw Error(l+t+" not an object: "+i);G=i}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(i=e[t])||/^.?$|[+\-.\s]|(.).*\1/.test(i))throw Error(l+t+" invalid: "+i);z="0123456789"==i.slice(0,10),L=i}}return{DECIMAL_PLACES:M,ROUNDING_MODE:F,EXPONENTIAL_AT:[R,D],RANGE:[B,$],CRYPTO:k,MODULO_MODE:j,POW_PRECISION:U,FORMAT:G,ALPHABET:L}},Z.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!Z.DEBUG)return!0;var t,i,r=e.c,n=e.e,o=e.s;e:if("[object Array]"=={}.toString.call(r)){if((1===o||-1===o)&&n>=-g&&n<=g&&n===a(n)){if(0===r[0]){if(0===n&&1===r.length)return!0;break e}if((t=(n+1)%f)<1&&(t+=f),String(r[0]).length==t){for(t=0;t<r.length;t++)if((i=r[t])<0||i>=p||i!==a(i))break e;if(0!==i)return!0}}}else if(null===r&&null===n&&(null===o||1===o||-1===o))return!0;throw Error(l+"Invalid BigNumber: "+e)},Z.maximum=Z.max=function(){return V(arguments,C.lt)},Z.minimum=Z.min=function(){return V(arguments,C.gt)},Z.random=(o=9007199254740992,I=Math.random()*o&2097151?function(){return a(Math.random()*o)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,i,r,n,o,s=0,u=[],p=new Z(A);if(null==e?e=M:v(e,0,g),n=c(e/f),k)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(n*=2));s<n;)(o=131072*t[s]+(t[s+1]>>>11))>=9e15?(i=crypto.getRandomValues(new Uint32Array(2)),t[s]=i[0],t[s+1]=i[1]):(u.push(o%1e14),s+=2);s=n/2}else{if(!crypto.randomBytes)throw k=!1,Error(l+"crypto unavailable");for(t=crypto.randomBytes(n*=7);s<n;)(o=281474976710656*(31&t[s])+1099511627776*t[s+1]+4294967296*t[s+2]+16777216*t[s+3]+(t[s+4]<<16)+(t[s+5]<<8)+t[s+6])>=9e15?crypto.randomBytes(7).copy(t,s):(u.push(o%1e14),s+=7);s=n/7}if(!k)for(;s<n;)(o=I())<9e15&&(u[s++]=o%1e14);for(n=u[--s],e%=f,n&&e&&(o=d[f-e],u[s]=a(n/o)*o);0===u[s];u.pop(),s--);if(s<0)u=[r=0];else{for(r=-1;0===u[0];u.splice(0,1),r-=f);for(s=1,o=u[0];o>=10;o/=10,s++);s<f&&(r-=f-s)}return p.e=r,p.c=u,p}),Z.sum=function(){for(var e=1,t=arguments,i=new Z(t[0]);e<t.length;)i=i.plus(t[e++]);return i},r=function(){var e="0123456789";function t(e,t,i,r){for(var n,o,s=[0],c=0,a=e.length;c<a;){for(o=s.length;o--;s[o]*=t);for(s[0]+=r.indexOf(e.charAt(c++)),n=0;n<s.length;n++)s[n]>i-1&&(null==s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/i|0,s[n]%=i)}return s.reverse()}return function(r,n,o,s,c){var a,l,u,p,f,h,d,m,g=r.indexOf("."),b=M,y=F;for(g>=0&&(p=U,U=0,r=r.replace(".",""),h=(m=new Z(n)).pow(r.length-g),U=p,m.c=t(_(w(h.c),h.e,"0"),10,o,e),m.e=m.c.length),u=p=(d=t(r,n,o,c?(a=L,e):(a=e,L))).length;0==d[--p];d.pop());if(!d[0])return a.charAt(0);if(g<0?--u:(h.c=d,h.e=u,h.s=s,d=(h=i(h,m,b,y,o)).c,f=h.r,u=h.e),g=d[l=u+b+1],p=o/2,f=f||l<0||null!=d[l+1],f=y<4?(null!=g||f)&&(0==y||y==(h.s<0?3:2)):g>p||g==p&&(4==y||f||6==y&&1&d[l-1]||y==(h.s<0?8:7)),l<1||!d[0])r=f?_(a.charAt(1),-b,a.charAt(0)):a.charAt(0);else{if(d.length=l,f)for(--o;++d[--l]>o;)d[l]=0,l||(++u,d=[1].concat(d));for(p=d.length;!d[--p];);for(g=0,r="";g<=p;r+=a.charAt(d[g++]));r=_(r,u,a.charAt(0))}return r}}(),i=function(){function e(e,t,i){var r,n,o,s,c=0,a=e.length,l=t%m,u=t/m|0;for(e=e.slice();a--;)c=((n=l*(o=e[a]%m)+(r=u*o+(s=e[a]/m|0)*l)%m*m+c)/i|0)+(r/m|0)+u*s,e[a]=n%i;return c&&(e=[c].concat(e)),e}function t(e,t,i,r){var n,o;if(i!=r)o=i>r?1:-1;else for(n=o=0;n<i;n++)if(e[n]!=t[n]){o=e[n]>t[n]?1:-1;break}return o}function i(e,t,i,r){for(var n=0;i--;)e[i]-=n,n=e[i]<t[i]?1:0,e[i]=n*r+e[i]-t[i];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(r,n,o,s,c){var l,u,h,d,m,g,w,y,v,x,S,_,I,E,N,P,O,T=r.s==n.s?1:-1,C=r.c,A=n.c;if(!(C&&C[0]&&A&&A[0]))return new Z(r.s&&n.s&&(C?!A||C[0]!=A[0]:A)?C&&0==C[0]||!A?0*T:T/0:NaN);for(v=(y=new Z(T)).c=[],T=o+(u=r.e-n.e)+1,c||(c=p,u=b(r.e/f)-b(n.e/f),T=T/f|0),h=0;A[h]==(C[h]||0);h++);if(A[h]>(C[h]||0)&&u--,T<0)v.push(1),d=!0;else{for(E=C.length,P=A.length,h=0,T+=2,(m=a(c/(A[0]+1)))>1&&(A=e(A,m,c),C=e(C,m,c),P=A.length,E=C.length),I=P,S=(x=C.slice(0,P)).length;S<P;x[S++]=0);O=A.slice(),O=[0].concat(O),N=A[0],A[1]>=c/2&&N++;do{if(m=0,(l=t(A,x,P,S))<0){if(_=x[0],P!=S&&(_=_*c+(x[1]||0)),(m=a(_/N))>1)for(m>=c&&(m=c-1),w=(g=e(A,m,c)).length,S=x.length;1==t(g,x,w,S);)m--,i(g,P<w?O:A,w,c),w=g.length,l=1;else 0==m&&(l=m=1),w=(g=A.slice()).length;if(w<S&&(g=[0].concat(g)),i(x,g,S,c),S=x.length,-1==l)for(;t(A,x,P,S)<1;)m++,i(x,P<S?O:A,S,c),S=x.length}else 0===l&&(m++,x=[0]);v[h++]=m,x[0]?x[S++]=C[I]||0:(x=[C[I]],S=1)}while((I++<E||null!=x[0])&&T--);d=null!=x[0],v[0]||v.splice(0,1)}if(c==p){for(h=1,T=v[0];T>=10;T/=10,h++);K(y,o+(y.e=h+u*f-1)+1,s,d)}else y.e=u,y.r=+d;return y}}(),E=/^(-?)0([xbo])(?=\w[\w.]*$)/i,N=/^([^.]+)\.$/,P=/^\.([^.]+)$/,O=/^-?(Infinity|NaN)$/,T=/^\s*\+(?=[\w.])|^\s+|\s+$/g,n=function(e,t,i,r){var n,o=i?t:t.replace(T,"");if(O.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!i&&(o=o.replace(E,(function(e,t,i){return n="x"==(i=i.toLowerCase())?16:"b"==i?2:8,r&&r!=n?e:t})),r&&(n=r,o=o.replace(N,"$1").replace(P,"0.$1")),t!=o))return new Z(o,n);if(Z.DEBUG)throw Error(l+"Not a"+(r?" base "+r:"")+" number: "+t);e.s=null}e.c=e.e=null},C.absoluteValue=C.abs=function(){var e=new Z(this);return e.s<0&&(e.s=1),e},C.comparedTo=function(e,t){return y(this,new Z(e,t))},C.decimalPlaces=C.dp=function(e,t){var i,r,n,o=this;if(null!=e)return v(e,0,g),null==t?t=F:v(t,0,8),K(new Z(o),e+o.e+1,t);if(!(i=o.c))return null;if(r=((n=i.length-1)-b(this.e/f))*f,n=i[n])for(;n%10==0;n/=10,r--);return r<0&&(r=0),r},C.dividedBy=C.div=function(e,t){return i(this,new Z(e,t),M,F)},C.dividedToIntegerBy=C.idiv=function(e,t){return i(this,new Z(e,t),0,1)},C.exponentiatedBy=C.pow=function(e,t){var i,r,n,o,s,u,p,h,d=this;if((e=new Z(e)).c&&!e.isInteger())throw Error(l+"Exponent not an integer: "+W(e));if(null!=t&&(t=new Z(t)),s=e.e>14,!d.c||!d.c[0]||1==d.c[0]&&!d.e&&1==d.c.length||!e.c||!e.c[0])return h=new Z(Math.pow(+W(d),s?e.s*(2-x(e)):+W(e))),t?h.mod(t):h;if(u=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new Z(NaN);(r=!u&&d.isInteger()&&t.isInteger())&&(d=d.mod(t))}else{if(e.e>9&&(d.e>0||d.e<-1||(0==d.e?d.c[0]>1||s&&d.c[1]>=24e7:d.c[0]<8e13||s&&d.c[0]<=9999975e7)))return o=d.s<0&&x(e)?-0:0,d.e>-1&&(o=1/o),new Z(u?1/o:o);U&&(o=c(U/f+2))}for(s?(i=new Z(.5),u&&(e.s=1),p=x(e)):p=(n=Math.abs(+W(e)))%2,h=new Z(A);;){if(p){if(!(h=h.times(d)).c)break;o?h.c.length>o&&(h.c.length=o):r&&(h=h.mod(t))}if(n){if(0===(n=a(n/2)))break;p=n%2}else if(K(e=e.times(i),e.e+1,1),e.e>14)p=x(e);else{if(0===(n=+W(e)))break;p=n%2}d=d.times(d),o?d.c&&d.c.length>o&&(d.c.length=o):r&&(d=d.mod(t))}return r?h:(u&&(h=A.div(h)),t?h.mod(t):o?K(h,U,F,undefined):h)},C.integerValue=function(e){var t=new Z(this);return null==e?e=F:v(e,0,8),K(t,t.e+1,e)},C.isEqualTo=C.eq=function(e,t){return 0===y(this,new Z(e,t))},C.isFinite=function(){return!!this.c},C.isGreaterThan=C.gt=function(e,t){return y(this,new Z(e,t))>0},C.isGreaterThanOrEqualTo=C.gte=function(e,t){return 1===(t=y(this,new Z(e,t)))||0===t},C.isInteger=function(){return!!this.c&&b(this.e/f)>this.c.length-2},C.isLessThan=C.lt=function(e,t){return y(this,new Z(e,t))<0},C.isLessThanOrEqualTo=C.lte=function(e,t){return-1===(t=y(this,new Z(e,t)))||0===t},C.isNaN=function(){return!this.s},C.isNegative=function(){return this.s<0},C.isPositive=function(){return this.s>0},C.isZero=function(){return!!this.c&&0==this.c[0]},C.minus=function(e,t){var i,r,n,o,s=this,c=s.s;if(t=(e=new Z(e,t)).s,!c||!t)return new Z(NaN);if(c!=t)return e.s=-t,s.plus(e);var a=s.e/f,l=e.e/f,u=s.c,h=e.c;if(!a||!l){if(!u||!h)return u?(e.s=-t,e):new Z(h?s:NaN);if(!u[0]||!h[0])return h[0]?(e.s=-t,e):new Z(u[0]?s:3==F?-0:0)}if(a=b(a),l=b(l),u=u.slice(),c=a-l){for((o=c<0)?(c=-c,n=u):(l=a,n=h),n.reverse(),t=c;t--;n.push(0));n.reverse()}else for(r=(o=(c=u.length)<(t=h.length))?c:t,c=t=0;t<r;t++)if(u[t]!=h[t]){o=u[t]<h[t];break}if(o&&(n=u,u=h,h=n,e.s=-e.s),(t=(r=h.length)-(i=u.length))>0)for(;t--;u[i++]=0);for(t=p-1;r>c;){if(u[--r]<h[r]){for(i=r;i&&!u[--i];u[i]=t);--u[i],u[r]+=p}u[r]-=h[r]}for(;0==u[0];u.splice(0,1),--l);return u[0]?H(e,u,l):(e.s=3==F?-1:1,e.c=[e.e=0],e)},C.modulo=C.mod=function(e,t){var r,n,o=this;return e=new Z(e,t),!o.c||!e.s||e.c&&!e.c[0]?new Z(NaN):!e.c||o.c&&!o.c[0]?new Z(o):(9==j?(n=e.s,e.s=1,r=i(o,e,0,3),e.s=n,r.s*=n):r=i(o,e,0,j),(e=o.minus(r.times(e))).c[0]||1!=j||(e.s=o.s),e)},C.multipliedBy=C.times=function(e,t){var i,r,n,o,s,c,a,l,u,h,d,g,w,y,v,x=this,S=x.c,_=(e=new Z(e,t)).c;if(!(S&&_&&S[0]&&_[0]))return!x.s||!e.s||S&&!S[0]&&!_||_&&!_[0]&&!S?e.c=e.e=e.s=null:(e.s*=x.s,S&&_?(e.c=[0],e.e=0):e.c=e.e=null),e;for(r=b(x.e/f)+b(e.e/f),e.s*=x.s,(a=S.length)<(h=_.length)&&(w=S,S=_,_=w,n=a,a=h,h=n),n=a+h,w=[];n--;w.push(0));for(y=p,v=m,n=h;--n>=0;){for(i=0,d=_[n]%v,g=_[n]/v|0,o=n+(s=a);o>n;)i=((l=d*(l=S[--s]%v)+(c=g*l+(u=S[s]/v|0)*d)%v*v+w[o]+i)/y|0)+(c/v|0)+g*u,w[o--]=l%y;w[o]=i}return i?++r:w.splice(0,1),H(e,w,r)},C.negated=function(){var e=new Z(this);return e.s=-e.s||null,e},C.plus=function(e,t){var i,r=this,n=r.s;if(t=(e=new Z(e,t)).s,!n||!t)return new Z(NaN);if(n!=t)return e.s=-t,r.minus(e);var o=r.e/f,s=e.e/f,c=r.c,a=e.c;if(!o||!s){if(!c||!a)return new Z(n/0);if(!c[0]||!a[0])return a[0]?e:new Z(c[0]?r:0*n)}if(o=b(o),s=b(s),c=c.slice(),n=o-s){for(n>0?(s=o,i=a):(n=-n,i=c),i.reverse();n--;i.push(0));i.reverse()}for((n=c.length)-(t=a.length)<0&&(i=a,a=c,c=i,t=n),n=0;t;)n=(c[--t]=c[t]+a[t]+n)/p|0,c[t]=p===c[t]?0:c[t]%p;return n&&(c=[n].concat(c),++s),H(e,c,s)},C.precision=C.sd=function(e,t){var i,r,n,o=this;if(null!=e&&e!==!!e)return v(e,1,g),null==t?t=F:v(t,0,8),K(new Z(o),e,t);if(!(i=o.c))return null;if(r=(n=i.length-1)*f+1,n=i[n]){for(;n%10==0;n/=10,r--);for(n=i[0];n>=10;n/=10,r++);}return e&&o.e+1>r&&(r=o.e+1),r},C.shiftedBy=function(e){return v(e,-9007199254740991,h),this.times("1e"+e)},C.squareRoot=C.sqrt=function(){var e,t,r,n,o,s=this,c=s.c,a=s.s,l=s.e,u=M+4,p=new Z("0.5");if(1!==a||!c||!c[0])return new Z(!a||a<0&&(!c||c[0])?NaN:c?s:1/0);if(0==(a=Math.sqrt(+W(s)))||a==1/0?(((t=w(c)).length+l)%2==0&&(t+="0"),a=Math.sqrt(+t),l=b((l+1)/2)-(l<0||l%2),r=new Z(t=a==1/0?"5e"+l:(t=a.toExponential()).slice(0,t.indexOf("e")+1)+l)):r=new Z(a+""),r.c[0])for((a=(l=r.e)+u)<3&&(a=0);;)if(o=r,r=p.times(o.plus(i(s,o,u,1))),w(o.c).slice(0,a)===(t=w(r.c)).slice(0,a)){if(r.e<l&&--a,"9999"!=(t=t.slice(a-3,a+1))&&(n||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(K(r,r.e+M+2,1),e=!r.times(r).eq(s));break}if(!n&&(K(o,o.e+M+2,0),o.times(o).eq(s))){r=o;break}u+=4,a+=4,n=1}return K(r,r.e+M+1,F,e)},C.toExponential=function(e,t){return null!=e&&(v(e,0,g),e++),q(this,e,t,1)},C.toFixed=function(e,t){return null!=e&&(v(e,0,g),e=e+this.e+1),q(this,e,t)},C.toFormat=function(e,t,i){var r,n=this;if(null==i)null!=e&&t&&"object"==typeof t?(i=t,t=null):e&&"object"==typeof e?(i=e,e=t=null):i=G;else if("object"!=typeof i)throw Error(l+"Argument not an object: "+i);if(r=n.toFixed(e,t),n.c){var o,s=r.split("."),c=+i.groupSize,a=+i.secondaryGroupSize,u=i.groupSeparator||"",p=s[0],f=s[1],h=n.s<0,d=h?p.slice(1):p,m=d.length;if(a&&(o=c,c=a,a=o,m-=o),c>0&&m>0){for(o=m%c||c,p=d.substr(0,o);o<m;o+=c)p+=u+d.substr(o,c);a>0&&(p+=u+d.slice(o)),h&&(p="-"+p)}r=f?p+(i.decimalSeparator||"")+((a=+i.fractionGroupSize)?f.replace(new RegExp("\\d{"+a+"}\\B","g"),"$&"+(i.fractionGroupSeparator||"")):f):p}return(i.prefix||"")+r+(i.suffix||"")},C.toFraction=function(e){var t,r,n,o,s,c,a,u,p,h,m,g,b=this,y=b.c;if(null!=e&&(!(a=new Z(e)).isInteger()&&(a.c||1!==a.s)||a.lt(A)))throw Error(l+"Argument "+(a.isInteger()?"out of range: ":"not an integer: ")+W(a));if(!y)return new Z(b);for(t=new Z(A),p=r=new Z(A),n=u=new Z(A),g=w(y),s=t.e=g.length-b.e-1,t.c[0]=d[(c=s%f)<0?f+c:c],e=!e||a.comparedTo(t)>0?s>0?t:p:a,c=$,$=1/0,a=new Z(g),u.c[0]=0;h=i(a,t,0,1),1!=(o=r.plus(h.times(n))).comparedTo(e);)r=n,n=o,p=u.plus(h.times(o=p)),u=o,t=a.minus(h.times(o=t)),a=o;return o=i(e.minus(r),n,0,1),u=u.plus(o.times(p)),r=r.plus(o.times(n)),u.s=p.s=b.s,m=i(p,n,s*=2,F).minus(b).abs().comparedTo(i(u,r,s,F).minus(b).abs())<1?[p,n]:[u,r],$=c,m},C.toNumber=function(){return+W(this)},C.toPrecision=function(e,t){return null!=e&&v(e,1,g),q(this,e,t,2)},C.toString=function(e){var t,i=this,n=i.s,o=i.e;return null===o?n?(t="Infinity",n<0&&(t="-"+t)):t="NaN":(null==e?t=o<=R||o>=D?S(w(i.c),o):_(w(i.c),o,"0"):10===e&&z?t=_(w((i=K(new Z(i),M+o+1,F)).c),i.e,"0"):(v(e,2,L.length,"Base"),t=r(_(w(i.c),o,"0"),10,e,n,!0)),n<0&&i.c[0]&&(t="-"+t)),t},C.valueOf=C.toJSON=function(){return W(this)},C._isBigNumber=!0,null!=t&&Z.set(t),Z}(),o.default=o.BigNumber=o,void 0===(r=function(){return o}.call(t,i,t,e))||(e.exports=r)}()},5158:(e,t,i)=>{"use strict";i.d(t,{Z:()=>r});const r=
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class{constructor(e){this.message=e,this.name="LocalizationException"}}},1527:(e,t,i)=>{"use strict";i.d(t,{NumberFormatter:()=>a});var r=i(9475),n=i(3368),o=i(6965);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const s=i(1658);class c{constructor(e){this.numberSpecification=e}format(e,t){void 0!==t&&(this.numberSpecification=t);const i=Math.abs(e).toFixed(this.numberSpecification.getMaxFractionDigits());let[r,n]=this.extractMajorMinorDigits(i);r=this.splitMajorGroups(r),n=this.adjustMinorDigitsZeroes(n);let o=r;n&&(o+="."+n);const s=this.getCldrPattern(e<0);return o=this.addPlaceholders(o,s),o=this.replaceSymbols(o),o=this.performSpecificReplacements(o),o}extractMajorMinorDigits(e){const t=e.toString().split(".");return[t[0],void 0===t[1]?"":t[1]]}splitMajorGroups(e){if(!this.numberSpecification.isGroupingUsed())return e;const t=e.split("").reverse();let i=[];for(i.push(t.splice(0,this.numberSpecification.getPrimaryGroupSize()));t.length;)i.push(t.splice(0,this.numberSpecification.getSecondaryGroupSize()));i=i.reverse();const r=[];return i.forEach((e=>{r.push(e.reverse().join(""))})),r.join(",")}adjustMinorDigitsZeroes(e){let t=e;return t.length>this.numberSpecification.getMaxFractionDigits()&&(t=t.replace(/0+$/,"")),t.length<this.numberSpecification.getMinFractionDigits()&&(t=t.padEnd(this.numberSpecification.getMinFractionDigits(),"0")),t}getCldrPattern(e){return e?this.numberSpecification.getNegativePattern():this.numberSpecification.getPositivePattern()}replaceSymbols(e){const t=this.numberSpecification.getSymbol(),i={};return i["."]=t.getDecimal(),i[","]=t.getGroup(),i["-"]=t.getMinusSign(),i["%"]=t.getPercentSign(),i["+"]=t.getPlusSign(),this.strtr(e,i)}strtr(e,t){const i=Object.keys(t).map(s);return e.split(RegExp(`(${i.join("|")})`)).map((e=>t[e]||e)).join("")}addPlaceholders(e,t){return t.replace(/#?(,#+)*0(\.[0#]+)*/,e)}performSpecificReplacements(e){return this.numberSpecification instanceof n.Z?e.split("¤").join(this.numberSpecification.getCurrencySymbol()):e}static build(e){let t,i;return t=void 0!==e.numberSymbols?new r.Z(...e.numberSymbols):new r.Z(...e.symbol),i=e.currencySymbol?new n.Z(e.positivePattern,e.negativePattern,t,parseInt(e.maxFractionDigits,10),parseInt(e.minFractionDigits,10),e.groupingUsed,e.primaryGroupSize,e.secondaryGroupSize,e.currencySymbol,e.currencyCode):new o.Z(e.positivePattern,e.negativePattern,t,parseInt(e.maxFractionDigits,10),parseInt(e.minFractionDigits,10),e.groupingUsed,e.primaryGroupSize,e.secondaryGroupSize),new c(i)}}const a=c}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */,9475:(e,t,i)=>{"use strict";i.d(t,{Z:()=>n});var r=i(5158);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const n=class{constructor(e,t,i,r,n,o,s,c,a,l,u){this.decimal=e,this.group=t,this.list=i,this.percentSign=r,this.minusSign=n,this.plusSign=o,this.exponential=s,this.superscriptingExponent=c,this.perMille=a,this.infinity=l,this.nan=u,this.validateData()}getDecimal(){return this.decimal}getGroup(){return this.group}getList(){return this.list}getPercentSign(){return this.percentSign}getMinusSign(){return this.minusSign}getPlusSign(){return this.plusSign}getExponential(){return this.exponential}getSuperscriptingExponent(){return this.superscriptingExponent}getPerMille(){return this.perMille}getInfinity(){return this.infinity}getNan(){return this.nan}validateData(){if(!this.decimal||"string"!=typeof this.decimal)throw new r.Z("Invalid decimal");if(!this.group||"string"!=typeof this.group)throw new r.Z("Invalid group");if(!this.list||"string"!=typeof this.list)throw new r.Z("Invalid symbol list");if(!this.percentSign||"string"!=typeof this.percentSign)throw new r.Z("Invalid percentSign");if(!this.minusSign||"string"!=typeof this.minusSign)throw new r.Z("Invalid minusSign");if(!this.plusSign||"string"!=typeof this.plusSign)throw new r.Z("Invalid plusSign");if(!this.exponential||"string"!=typeof this.exponential)throw new r.Z("Invalid exponential");if(!this.superscriptingExponent||"string"!=typeof this.superscriptingExponent)throw new r.Z("Invalid superscriptingExponent");if(!this.perMille||"string"!=typeof this.perMille)throw new r.Z("Invalid perMille");if(!this.infinity||"string"!=typeof this.infinity)throw new r.Z("Invalid infinity");if(!this.nan||"string"!=typeof this.nan)throw new r.Z("Invalid nan")}}},6965:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var r=i(5158),n=i(9475);const o=
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class{constructor(e,t,i,o,s,c,a,l){if(this.positivePattern=e,this.negativePattern=t,this.symbol=i,this.maxFractionDigits=o,this.minFractionDigits=o<s?o:s,this.groupingUsed=c,this.primaryGroupSize=a,this.secondaryGroupSize=l,!this.positivePattern||"string"!=typeof this.positivePattern)throw new r.Z("Invalid positivePattern");if(!this.negativePattern||"string"!=typeof this.negativePattern)throw new r.Z("Invalid negativePattern");if(!(this.symbol&&this.symbol instanceof n.Z))throw new r.Z("Invalid symbol");if("number"!=typeof this.maxFractionDigits)throw new r.Z("Invalid maxFractionDigits");if("number"!=typeof this.minFractionDigits)throw new r.Z("Invalid minFractionDigits");if("boolean"!=typeof this.groupingUsed)throw new r.Z("Invalid groupingUsed");if("number"!=typeof this.primaryGroupSize)throw new r.Z("Invalid primaryGroupSize");if("number"!=typeof this.secondaryGroupSize)throw new r.Z("Invalid secondaryGroupSize")}getSymbol(){return this.symbol}getPositivePattern(){return this.positivePattern}getNegativePattern(){return this.negativePattern}getMaxFractionDigits(){return this.maxFractionDigits}getMinFractionDigits(){return this.minFractionDigits}isGroupingUsed(){return this.groupingUsed}getPrimaryGroupSize(){return this.primaryGroupSize}getSecondaryGroupSize(){return this.secondaryGroupSize}}},3368:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});var r=i(5158),n=i(6965);class o extends n.Z{constructor(e,t,i,n,o,s,c,a,l,u){if(super(e,t,i,n,o,s,c,a),this.currencySymbol=l,this.currencyCode=u,!this.currencySymbol||"string"!=typeof this.currencySymbol)throw new r.Z("Invalid currencySymbol");if(!this.currencyCode||"string"!=typeof this.currencyCode)throw new r.Z("Invalid currencyCode")}static getCurrencyDisplay(){return"symbol"}getCurrencySymbol(){return this.currencySymbol}getCurrencyCode(){return this.currencyCode}}const s=o},1658:(e,t,i)=>{var r="[object Symbol]",n=/[\\^$.*+?()[\]{}|]/g,o=RegExp(n.source),s="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,c="object"==typeof self&&self&&self.Object===Object&&self,a=s||c||Function("return this")(),l=Object.prototype.toString,u=a.Symbol,p=u?u.prototype:void 0,f=p?p.toString:void 0;function h(e){if("string"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&l.call(e)==r}(e))return f?f.call(e):"";var t=e+"";return"0"==t&&1/e==-Infinity?"-0":t}e.exports=function(e){var t;return(e=null==(t=e)?"":h(t))&&o.test(e)?e.replace(n,"\\$&"):e}},9567:e=>{"use strict";e.exports=window.jQuery}},t={};function i(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,i),o.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";i.r(r);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const e="#combination_list",t="combination-is-selected",n="bulk-select-all",o="bulk-select-all-in-page",s={navigationTab:"#product_combinations-tab-nav",navigationTarget:"#product_combinations-tab",combinationManager:"#product_combinations_combination_manager",preloader:"#combinations-preloader",emptyState:"#combinations-empty-state",emptyFiltersState:"#combinations-empty-filters-state",combinationsPaginatedList:"#combinations-paginated-list",combinationsFormContainer:"#combinations-list-form-container",combinationsFiltersContainer:"#combinations_filters",filtersSelectorButtons:"#combinations_filters .ps-checkboxes-dropdown button.dropdown-toggle",combinationsGeneratorContainer:"#product_combinations_generator",combinationsTable:`${e}`,combinationsTableBody:`${e} tbody`,combinationIdInputsSelector:".combination-id-input",deleteCombinationSelector:".delete-combination-item",deleteCombinationAllShopsSelector:".delete-combination-all-shops",combinationName:"form .combination-name-row .text-preview-value",paginationContainer:"#combinations-pagination",loadingSpinner:"#productCombinationsLoading",impactOnPriceInputWrapper:".combination-impact-on-price",referenceInputWrapper:".combination-reference",sortableColumns:".ps-sortable-column",combinationItemForm:{isDefaultKey:"combination_item[is_default]",deltaQuantityKey:"combination_item[delta_quantity][delta]",impactOnPriceKey:"combination_item[impact_on_price][value]",referenceKey:"combination_item[reference][value]",tokenKey:"combination_item[_token]"},editionForm:'form[name="combination_form"]',editionFormInputs:'form[name="combination_form"] input, form[name="combination_form"] textarea, form[name="combination_form"] select',editCombinationButtons:".edit-combination-item",tableRow:{isSelectedCombination:`.${t}`,combinationImg:".combination-image",deltaQuantityWrapper:".delta-quantity",deltaQuantityInput:t=>`${e}_combinations_${t}_delta_quantity_delta`,combinationCheckbox:t=>`${e}_combinations_${t}_is_selected`,combinationIdInput:t=>`${e}_combinations_${t}_combination_id`,combinationNameInput:t=>`${e}_combinations_${t}_name`,referenceInput:t=>`${e}_combinations_${t}_reference_value`,impactOnPriceInput:t=>`${e}_combinations_${t}_impact_on_price_value`,finalPriceTeInput:t=>`${e}_combinations_${t}_final_price_te`,quantityInput:t=>`${e}_combinations_${t}_delta_quantity_quantity`,isDefaultInput:t=>`${e}_combinations_${t}_is_default`,editButton:t=>`${e}_combinations_${t}_edit`,deleteButton:t=>`${e}_combinations_${t}_delete`},list:{attributeFilterInputName:"combination-attribute-filter",combinationRow:".combination-list-row",priceImpactTaxExcluded:".combination-impact-on-price-tax-excluded",priceImpactTaxIncluded:".combination-impact-on-price-tax-included",isDefault:".combination-is-default-input",ecoTax:".combination-eco-tax",finalPrice:".combination-final-price",finalPricePreview:".text-preview",modifiedFieldClass:"combination-value-changed",invalidClass:"is-invalid",editionModeClass:"combination-edition-mode",fieldInputs:`.combination-list-row :input:not(.${n}):not(.${t})`,errorAlerts:".combination-list-row .alert-danger",rowActionButtons:".combination-row-actions button, .combination-row-actions .dropdown-toggle",footer:{cancel:"#cancel-combinations-edition",save:"#save-combinations-edition"}},availabilityContainer:".combination-availability",editModal:"#combination-edit-modal",images:{selectorContainer:".combination-images-selector",imageChoice:".combination-image-choice",checkboxContainer:".form-check",checkbox:"input[type=checkbox]"},scrollBar:".attributes-list-overflow",searchInput:"#product-combinations-generate .attributes-search",generateCombinationsButton:".generate-combinations-button",bulkCombinationFormBtn:"#combination-bulk-form-btn",bulkDeleteBtn:".bulk-delete-btn",bulkDeleteBtnAllShopsId:"combination-bulk-delete-btn-all-shops",bulkActionBtn:".bulk-action-btn",bulkActionsDropdownBtn:"#combination-bulk-actions-btn",bulkAllPreviewInput:"#bulk-all-preview",bulkSelectAll:"#bulk-select-all",bulkCheckboxesDropdownButton:"#bulk-all-selection-dropdown-button",commonBulkAllSelector:`.${n}`,bulkSelectAllInPage:`#${o}`,bulkSelectAllInPageId:o,bulkProgressModalId:"bulk-combination-progress-modal",bulkFormModalId:"bulk-combination-form-modal",bulkForm:'form[name="bulk_combination"]',bulkDeltaQuantitySwitchName:"bulk_combination[stock][disabling_switch_delta_quantity]",bulkFixedQuantitySwitchName:"bulk_combination[stock][disabling_switch_fixed_quantity]"},{$:c}=window;class a{constructor(){this.$selectorContainer=c(s.images.selectorContainer),this.init()}init(){c(s.images.checkboxContainer,this.$selectorContainer).hide(),this.$selectorContainer.on("click",s.images.imageChoice,(e=>{if(this.$selectorContainer.hasClass("disabled"))return;const t=c(e.currentTarget),i=c(s.images.checkbox,t),r=i.prop("checked");t.toggleClass("selected",!r),i.prop("checked",!r)}))}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const l="form[name=combination_form]",u={productSuppliers:"#combination_form_product_suppliers"};var p=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,f=Math.ceil,h=Math.floor,d="[BigNumber Error] ",m=d+"Number primitive has more than 15 significant digits: ",g=1e14,b=14,w=9007199254740991,y=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],v=1e7,x=1e9;function S(e){var t=0|e;return e>0||e===t?t:t-1}function _(e){for(var t,i,r=1,n=e.length,o=e[0]+"";r<n;){for(t=e[r++]+"",i=b-t.length;i--;t="0"+t);o+=t}for(n=o.length;48===o.charCodeAt(--n););return o.slice(0,n+1||1)}function I(e,t){var i,r,n=e.c,o=t.c,s=e.s,c=t.s,a=e.e,l=t.e;if(!s||!c)return null;if(i=n&&!n[0],r=o&&!o[0],i||r)return i?r?0:-c:s;if(s!=c)return s;if(i=s<0,r=a==l,!n||!o)return r?0:!n^i?1:-1;if(!r)return a>l^i?1:-1;for(c=(a=n.length)<(l=o.length)?a:l,s=0;s<c;s++)if(n[s]!=o[s])return n[s]>o[s]^i?1:-1;return a==l?0:a>l^i?1:-1}function E(e,t,i,r){if(e<t||e>i||e!==h(e))throw Error(d+(r||"Argument")+("number"==typeof e?e<t||e>i?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function N(e){var t=e.c.length-1;return S(e.e/b)==t&&e.c[t]%2!=0}function P(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function O(e,t,i){var r,n;if(t<0){for(n=i+".";++t;n+=i);e=n+e}else if(++t>(r=e.length)){for(n=i,t-=r;--t;n+=i);e+=n}else t<r&&(e=e.slice(0,t)+"."+e.slice(t));return e}var T=function e(t){var i,r,n,o,s,c,a,l,u,T,C=Z.prototype={constructor:Z,toString:null,valueOf:null},A=new Z(1),M=20,F=4,R=-7,D=21,B=-1e7,$=1e7,k=!1,j=1,U=0,G={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},L="0123456789abcdefghijklmnopqrstuvwxyz",z=!0;function Z(e,t){var i,o,s,c,a,l,u,f,d=this;if(!(d instanceof Z))return new Z(e,t);if(null==t){if(e&&!0===e._isBigNumber)return d.s=e.s,void(!e.c||e.e>$?d.c=d.e=null:e.e<B?d.c=[d.e=0]:(d.e=e.e,d.c=e.c.slice()));if((l="number"==typeof e)&&0*e==0){if(d.s=1/e<0?(e=-e,-1):1,e===~~e){for(c=0,a=e;a>=10;a/=10,c++);return void(c>$?d.c=d.e=null:(d.e=c,d.c=[e]))}f=String(e)}else{if(!p.test(f=String(e)))return n(d,f,l);d.s=45==f.charCodeAt(0)?(f=f.slice(1),-1):1}(c=f.indexOf("."))>-1&&(f=f.replace(".","")),(a=f.search(/e/i))>0?(c<0&&(c=a),c+=+f.slice(a+1),f=f.substring(0,a)):c<0&&(c=f.length)}else{if(E(t,2,L.length,"Base"),10==t&&z)return K(d=new Z(e),M+d.e+1,F);if(f=String(e),l="number"==typeof e){if(0*e!=0)return n(d,f,l,t);if(d.s=1/e<0?(f=f.slice(1),-1):1,Z.DEBUG&&f.replace(/^0\.0*|\./,"").length>15)throw Error(m+e)}else d.s=45===f.charCodeAt(0)?(f=f.slice(1),-1):1;for(i=L.slice(0,t),c=a=0,u=f.length;a<u;a++)if(i.indexOf(o=f.charAt(a))<0){if("."==o){if(a>c){c=u;continue}}else if(!s&&(f==f.toUpperCase()&&(f=f.toLowerCase())||f==f.toLowerCase()&&(f=f.toUpperCase()))){s=!0,a=-1,c=0;continue}return n(d,String(e),l,t)}l=!1,(c=(f=r(f,t,10,d.s)).indexOf("."))>-1?f=f.replace(".",""):c=f.length}for(a=0;48===f.charCodeAt(a);a++);for(u=f.length;48===f.charCodeAt(--u););if(f=f.slice(a,++u)){if(u-=a,l&&Z.DEBUG&&u>15&&(e>w||e!==h(e)))throw Error(m+d.s*e);if((c=c-a-1)>$)d.c=d.e=null;else if(c<B)d.c=[d.e=0];else{if(d.e=c,d.c=[],a=(c+1)%b,c<0&&(a+=b),a<u){for(a&&d.c.push(+f.slice(0,a)),u-=b;a<u;)d.c.push(+f.slice(a,a+=b));a=b-(f=f.slice(a)).length}else a-=u;for(;a--;f+="0");d.c.push(+f)}}else d.c=[d.e=0]}function q(e,t,i,r){var n,o,s,c,a;if(null==i?i=F:E(i,0,8),!e.c)return e.toString();if(n=e.c[0],s=e.e,null==t)a=_(e.c),a=1==r||2==r&&(s<=R||s>=D)?P(a,s):O(a,s,"0");else if(o=(e=K(new Z(e),t,i)).e,c=(a=_(e.c)).length,1==r||2==r&&(t<=o||o<=R)){for(;c<t;a+="0",c++);a=P(a,o)}else if(t-=s,a=O(a,o,"0"),o+1>c){if(--t>0)for(a+=".";t--;a+="0");}else if((t+=o-c)>0)for(o+1==c&&(a+=".");t--;a+="0");return e.s<0&&n?"-"+a:a}function V(e,t){for(var i,r=1,n=new Z(e[0]);r<e.length;r++){if(!(i=new Z(e[r])).s){n=i;break}t.call(n,i)&&(n=i)}return n}function H(e,t,i){for(var r=1,n=t.length;!t[--n];t.pop());for(n=t[0];n>=10;n/=10,r++);return(i=r+i*b-1)>$?e.c=e.e=null:i<B?e.c=[e.e=0]:(e.e=i,e.c=t),e}function K(e,t,i,r){var n,o,s,c,a,l,u,p=e.c,d=y;if(p){e:{for(n=1,c=p[0];c>=10;c/=10,n++);if((o=t-n)<0)o+=b,s=t,u=(a=p[l=0])/d[n-s-1]%10|0;else if((l=f((o+1)/b))>=p.length){if(!r)break e;for(;p.length<=l;p.push(0));a=u=0,n=1,s=(o%=b)-b+1}else{for(a=c=p[l],n=1;c>=10;c/=10,n++);u=(s=(o%=b)-b+n)<0?0:a/d[n-s-1]%10|0}if(r=r||t<0||null!=p[l+1]||(s<0?a:a%d[n-s-1]),r=i<4?(u||r)&&(0==i||i==(e.s<0?3:2)):u>5||5==u&&(4==i||r||6==i&&(o>0?s>0?a/d[n-s]:0:p[l-1])%10&1||i==(e.s<0?8:7)),t<1||!p[0])return p.length=0,r?(t-=e.e+1,p[0]=d[(b-t%b)%b],e.e=-t||0):p[0]=e.e=0,e;if(0==o?(p.length=l,c=1,l--):(p.length=l+1,c=d[b-o],p[l]=s>0?h(a/d[n-s]%d[s])*c:0),r)for(;;){if(0==l){for(o=1,s=p[0];s>=10;s/=10,o++);for(s=p[0]+=c,c=1;s>=10;s/=10,c++);o!=c&&(e.e++,p[0]==g&&(p[0]=1));break}if(p[l]+=c,p[l]!=g)break;p[l--]=0,c=1}for(o=p.length;0===p[--o];p.pop());}e.e>$?e.c=e.e=null:e.e<B&&(e.c=[e.e=0])}return e}function W(e){var t,i=e.e;return null===i?e.toString():(t=_(e.c),t=i<=R||i>=D?P(t,i):O(t,i,"0"),e.s<0?"-"+t:t)}return Z.clone=e,Z.ROUND_UP=0,Z.ROUND_DOWN=1,Z.ROUND_CEIL=2,Z.ROUND_FLOOR=3,Z.ROUND_HALF_UP=4,Z.ROUND_HALF_DOWN=5,Z.ROUND_HALF_EVEN=6,Z.ROUND_HALF_CEIL=7,Z.ROUND_HALF_FLOOR=8,Z.EUCLID=9,Z.config=Z.set=function(e){var t,i;if(null!=e){if("object"!=typeof e)throw Error(d+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(E(i=e[t],0,x,t),M=i),e.hasOwnProperty(t="ROUNDING_MODE")&&(E(i=e[t],0,8,t),F=i),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((i=e[t])&&i.pop?(E(i[0],-x,0,t),E(i[1],0,x,t),R=i[0],D=i[1]):(E(i,-x,x,t),R=-(D=i<0?-i:i))),e.hasOwnProperty(t="RANGE"))if((i=e[t])&&i.pop)E(i[0],-x,-1,t),E(i[1],1,x,t),B=i[0],$=i[1];else{if(E(i,-x,x,t),!i)throw Error(d+t+" cannot be zero: "+i);B=-($=i<0?-i:i)}if(e.hasOwnProperty(t="CRYPTO")){if((i=e[t])!==!!i)throw Error(d+t+" not true or false: "+i);if(i){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw k=!i,Error(d+"crypto unavailable");k=i}else k=i}if(e.hasOwnProperty(t="MODULO_MODE")&&(E(i=e[t],0,9,t),j=i),e.hasOwnProperty(t="POW_PRECISION")&&(E(i=e[t],0,x,t),U=i),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(i=e[t]))throw Error(d+t+" not an object: "+i);G=i}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(i=e[t])||/^.?$|[+\-.\s]|(.).*\1/.test(i))throw Error(d+t+" invalid: "+i);z="0123456789"==i.slice(0,10),L=i}}return{DECIMAL_PLACES:M,ROUNDING_MODE:F,EXPONENTIAL_AT:[R,D],RANGE:[B,$],CRYPTO:k,MODULO_MODE:j,POW_PRECISION:U,FORMAT:G,ALPHABET:L}},Z.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!Z.DEBUG)return!0;var t,i,r=e.c,n=e.e,o=e.s;e:if("[object Array]"=={}.toString.call(r)){if((1===o||-1===o)&&n>=-x&&n<=x&&n===h(n)){if(0===r[0]){if(0===n&&1===r.length)return!0;break e}if((t=(n+1)%b)<1&&(t+=b),String(r[0]).length==t){for(t=0;t<r.length;t++)if((i=r[t])<0||i>=g||i!==h(i))break e;if(0!==i)return!0}}}else if(null===r&&null===n&&(null===o||1===o||-1===o))return!0;throw Error(d+"Invalid BigNumber: "+e)},Z.maximum=Z.max=function(){return V(arguments,C.lt)},Z.minimum=Z.min=function(){return V(arguments,C.gt)},Z.random=(o=9007199254740992,s=Math.random()*o&2097151?function(){return h(Math.random()*o)}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,i,r,n,o,c=0,a=[],l=new Z(A);if(null==e?e=M:E(e,0,x),n=f(e/b),k)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(n*=2));c<n;)(o=131072*t[c]+(t[c+1]>>>11))>=9e15?(i=crypto.getRandomValues(new Uint32Array(2)),t[c]=i[0],t[c+1]=i[1]):(a.push(o%1e14),c+=2);c=n/2}else{if(!crypto.randomBytes)throw k=!1,Error(d+"crypto unavailable");for(t=crypto.randomBytes(n*=7);c<n;)(o=281474976710656*(31&t[c])+1099511627776*t[c+1]+4294967296*t[c+2]+16777216*t[c+3]+(t[c+4]<<16)+(t[c+5]<<8)+t[c+6])>=9e15?crypto.randomBytes(7).copy(t,c):(a.push(o%1e14),c+=7);c=n/7}if(!k)for(;c<n;)(o=s())<9e15&&(a[c++]=o%1e14);for(n=a[--c],e%=b,n&&e&&(o=y[b-e],a[c]=h(n/o)*o);0===a[c];a.pop(),c--);if(c<0)a=[r=0];else{for(r=-1;0===a[0];a.splice(0,1),r-=b);for(c=1,o=a[0];o>=10;o/=10,c++);c<b&&(r-=b-c)}return l.e=r,l.c=a,l}),Z.sum=function(){for(var e=1,t=arguments,i=new Z(t[0]);e<t.length;)i=i.plus(t[e++]);return i},r=function(){var e="0123456789";function t(e,t,i,r){for(var n,o,s=[0],c=0,a=e.length;c<a;){for(o=s.length;o--;s[o]*=t);for(s[0]+=r.indexOf(e.charAt(c++)),n=0;n<s.length;n++)s[n]>i-1&&(null==s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/i|0,s[n]%=i)}return s.reverse()}return function(r,n,o,s,c){var a,l,u,p,f,h,d,m,g=r.indexOf("."),b=M,w=F;for(g>=0&&(p=U,U=0,r=r.replace(".",""),h=(m=new Z(n)).pow(r.length-g),U=p,m.c=t(O(_(h.c),h.e,"0"),10,o,e),m.e=m.c.length),u=p=(d=t(r,n,o,c?(a=L,e):(a=e,L))).length;0==d[--p];d.pop());if(!d[0])return a.charAt(0);if(g<0?--u:(h.c=d,h.e=u,h.s=s,d=(h=i(h,m,b,w,o)).c,f=h.r,u=h.e),g=d[l=u+b+1],p=o/2,f=f||l<0||null!=d[l+1],f=w<4?(null!=g||f)&&(0==w||w==(h.s<0?3:2)):g>p||g==p&&(4==w||f||6==w&&1&d[l-1]||w==(h.s<0?8:7)),l<1||!d[0])r=f?O(a.charAt(1),-b,a.charAt(0)):a.charAt(0);else{if(d.length=l,f)for(--o;++d[--l]>o;)d[l]=0,l||(++u,d=[1].concat(d));for(p=d.length;!d[--p];);for(g=0,r="";g<=p;r+=a.charAt(d[g++]));r=O(r,u,a.charAt(0))}return r}}(),i=function(){function e(e,t,i){var r,n,o,s,c=0,a=e.length,l=t%v,u=t/v|0;for(e=e.slice();a--;)c=((n=l*(o=e[a]%v)+(r=u*o+(s=e[a]/v|0)*l)%v*v+c)/i|0)+(r/v|0)+u*s,e[a]=n%i;return c&&(e=[c].concat(e)),e}function t(e,t,i,r){var n,o;if(i!=r)o=i>r?1:-1;else for(n=o=0;n<i;n++)if(e[n]!=t[n]){o=e[n]>t[n]?1:-1;break}return o}function i(e,t,i,r){for(var n=0;i--;)e[i]-=n,n=e[i]<t[i]?1:0,e[i]=n*r+e[i]-t[i];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(r,n,o,s,c){var a,l,u,p,f,d,m,w,y,v,x,_,I,E,N,P,O,T=r.s==n.s?1:-1,C=r.c,A=n.c;if(!(C&&C[0]&&A&&A[0]))return new Z(r.s&&n.s&&(C?!A||C[0]!=A[0]:A)?C&&0==C[0]||!A?0*T:T/0:NaN);for(y=(w=new Z(T)).c=[],T=o+(l=r.e-n.e)+1,c||(c=g,l=S(r.e/b)-S(n.e/b),T=T/b|0),u=0;A[u]==(C[u]||0);u++);if(A[u]>(C[u]||0)&&l--,T<0)y.push(1),p=!0;else{for(E=C.length,P=A.length,u=0,T+=2,(f=h(c/(A[0]+1)))>1&&(A=e(A,f,c),C=e(C,f,c),P=A.length,E=C.length),I=P,x=(v=C.slice(0,P)).length;x<P;v[x++]=0);O=A.slice(),O=[0].concat(O),N=A[0],A[1]>=c/2&&N++;do{if(f=0,(a=t(A,v,P,x))<0){if(_=v[0],P!=x&&(_=_*c+(v[1]||0)),(f=h(_/N))>1)for(f>=c&&(f=c-1),m=(d=e(A,f,c)).length,x=v.length;1==t(d,v,m,x);)f--,i(d,P<m?O:A,m,c),m=d.length,a=1;else 0==f&&(a=f=1),m=(d=A.slice()).length;if(m<x&&(d=[0].concat(d)),i(v,d,x,c),x=v.length,-1==a)for(;t(A,v,P,x)<1;)f++,i(v,P<x?O:A,x,c),x=v.length}else 0===a&&(f++,v=[0]);y[u++]=f,v[0]?v[x++]=C[I]||0:(v=[C[I]],x=1)}while((I++<E||null!=v[0])&&T--);p=null!=v[0],y[0]||y.splice(0,1)}if(c==g){for(u=1,T=y[0];T>=10;T/=10,u++);K(w,o+(w.e=u+l*b-1)+1,s,p)}else w.e=l,w.r=+p;return w}}(),c=/^(-?)0([xbo])(?=\w[\w.]*$)/i,a=/^([^.]+)\.$/,l=/^\.([^.]+)$/,u=/^-?(Infinity|NaN)$/,T=/^\s*\+(?=[\w.])|^\s+|\s+$/g,n=function(e,t,i,r){var n,o=i?t:t.replace(T,"");if(u.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!i&&(o=o.replace(c,(function(e,t,i){return n="x"==(i=i.toLowerCase())?16:"b"==i?2:8,r&&r!=n?e:t})),r&&(n=r,o=o.replace(a,"$1").replace(l,"0.$1")),t!=o))return new Z(o,n);if(Z.DEBUG)throw Error(d+"Not a"+(r?" base "+r:"")+" number: "+t);e.s=null}e.c=e.e=null},C.absoluteValue=C.abs=function(){var e=new Z(this);return e.s<0&&(e.s=1),e},C.comparedTo=function(e,t){return I(this,new Z(e,t))},C.decimalPlaces=C.dp=function(e,t){var i,r,n,o=this;if(null!=e)return E(e,0,x),null==t?t=F:E(t,0,8),K(new Z(o),e+o.e+1,t);if(!(i=o.c))return null;if(r=((n=i.length-1)-S(this.e/b))*b,n=i[n])for(;n%10==0;n/=10,r--);return r<0&&(r=0),r},C.dividedBy=C.div=function(e,t){return i(this,new Z(e,t),M,F)},C.dividedToIntegerBy=C.idiv=function(e,t){return i(this,new Z(e,t),0,1)},C.exponentiatedBy=C.pow=function(e,t){var i,r,n,o,s,c,a,l,u=this;if((e=new Z(e)).c&&!e.isInteger())throw Error(d+"Exponent not an integer: "+W(e));if(null!=t&&(t=new Z(t)),s=e.e>14,!u.c||!u.c[0]||1==u.c[0]&&!u.e&&1==u.c.length||!e.c||!e.c[0])return l=new Z(Math.pow(+W(u),s?e.s*(2-N(e)):+W(e))),t?l.mod(t):l;if(c=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new Z(NaN);(r=!c&&u.isInteger()&&t.isInteger())&&(u=u.mod(t))}else{if(e.e>9&&(u.e>0||u.e<-1||(0==u.e?u.c[0]>1||s&&u.c[1]>=24e7:u.c[0]<8e13||s&&u.c[0]<=9999975e7)))return o=u.s<0&&N(e)?-0:0,u.e>-1&&(o=1/o),new Z(c?1/o:o);U&&(o=f(U/b+2))}for(s?(i=new Z(.5),c&&(e.s=1),a=N(e)):a=(n=Math.abs(+W(e)))%2,l=new Z(A);;){if(a){if(!(l=l.times(u)).c)break;o?l.c.length>o&&(l.c.length=o):r&&(l=l.mod(t))}if(n){if(0===(n=h(n/2)))break;a=n%2}else if(K(e=e.times(i),e.e+1,1),e.e>14)a=N(e);else{if(0===(n=+W(e)))break;a=n%2}u=u.times(u),o?u.c&&u.c.length>o&&(u.c.length=o):r&&(u=u.mod(t))}return r?l:(c&&(l=A.div(l)),t?l.mod(t):o?K(l,U,F,undefined):l)},C.integerValue=function(e){var t=new Z(this);return null==e?e=F:E(e,0,8),K(t,t.e+1,e)},C.isEqualTo=C.eq=function(e,t){return 0===I(this,new Z(e,t))},C.isFinite=function(){return!!this.c},C.isGreaterThan=C.gt=function(e,t){return I(this,new Z(e,t))>0},C.isGreaterThanOrEqualTo=C.gte=function(e,t){return 1===(t=I(this,new Z(e,t)))||0===t},C.isInteger=function(){return!!this.c&&S(this.e/b)>this.c.length-2},C.isLessThan=C.lt=function(e,t){return I(this,new Z(e,t))<0},C.isLessThanOrEqualTo=C.lte=function(e,t){return-1===(t=I(this,new Z(e,t)))||0===t},C.isNaN=function(){return!this.s},C.isNegative=function(){return this.s<0},C.isPositive=function(){return this.s>0},C.isZero=function(){return!!this.c&&0==this.c[0]},C.minus=function(e,t){var i,r,n,o,s=this,c=s.s;if(t=(e=new Z(e,t)).s,!c||!t)return new Z(NaN);if(c!=t)return e.s=-t,s.plus(e);var a=s.e/b,l=e.e/b,u=s.c,p=e.c;if(!a||!l){if(!u||!p)return u?(e.s=-t,e):new Z(p?s:NaN);if(!u[0]||!p[0])return p[0]?(e.s=-t,e):new Z(u[0]?s:3==F?-0:0)}if(a=S(a),l=S(l),u=u.slice(),c=a-l){for((o=c<0)?(c=-c,n=u):(l=a,n=p),n.reverse(),t=c;t--;n.push(0));n.reverse()}else for(r=(o=(c=u.length)<(t=p.length))?c:t,c=t=0;t<r;t++)if(u[t]!=p[t]){o=u[t]<p[t];break}if(o&&(n=u,u=p,p=n,e.s=-e.s),(t=(r=p.length)-(i=u.length))>0)for(;t--;u[i++]=0);for(t=g-1;r>c;){if(u[--r]<p[r]){for(i=r;i&&!u[--i];u[i]=t);--u[i],u[r]+=g}u[r]-=p[r]}for(;0==u[0];u.splice(0,1),--l);return u[0]?H(e,u,l):(e.s=3==F?-1:1,e.c=[e.e=0],e)},C.modulo=C.mod=function(e,t){var r,n,o=this;return e=new Z(e,t),!o.c||!e.s||e.c&&!e.c[0]?new Z(NaN):!e.c||o.c&&!o.c[0]?new Z(o):(9==j?(n=e.s,e.s=1,r=i(o,e,0,3),e.s=n,r.s*=n):r=i(o,e,0,j),(e=o.minus(r.times(e))).c[0]||1!=j||(e.s=o.s),e)},C.multipliedBy=C.times=function(e,t){var i,r,n,o,s,c,a,l,u,p,f,h,d,m,w,y=this,x=y.c,_=(e=new Z(e,t)).c;if(!(x&&_&&x[0]&&_[0]))return!y.s||!e.s||x&&!x[0]&&!_||_&&!_[0]&&!x?e.c=e.e=e.s=null:(e.s*=y.s,x&&_?(e.c=[0],e.e=0):e.c=e.e=null),e;for(r=S(y.e/b)+S(e.e/b),e.s*=y.s,(a=x.length)<(p=_.length)&&(d=x,x=_,_=d,n=a,a=p,p=n),n=a+p,d=[];n--;d.push(0));for(m=g,w=v,n=p;--n>=0;){for(i=0,f=_[n]%w,h=_[n]/w|0,o=n+(s=a);o>n;)i=((l=f*(l=x[--s]%w)+(c=h*l+(u=x[s]/w|0)*f)%w*w+d[o]+i)/m|0)+(c/w|0)+h*u,d[o--]=l%m;d[o]=i}return i?++r:d.splice(0,1),H(e,d,r)},C.negated=function(){var e=new Z(this);return e.s=-e.s||null,e},C.plus=function(e,t){var i,r=this,n=r.s;if(t=(e=new Z(e,t)).s,!n||!t)return new Z(NaN);if(n!=t)return e.s=-t,r.minus(e);var o=r.e/b,s=e.e/b,c=r.c,a=e.c;if(!o||!s){if(!c||!a)return new Z(n/0);if(!c[0]||!a[0])return a[0]?e:new Z(c[0]?r:0*n)}if(o=S(o),s=S(s),c=c.slice(),n=o-s){for(n>0?(s=o,i=a):(n=-n,i=c),i.reverse();n--;i.push(0));i.reverse()}for((n=c.length)-(t=a.length)<0&&(i=a,a=c,c=i,t=n),n=0;t;)n=(c[--t]=c[t]+a[t]+n)/g|0,c[t]=g===c[t]?0:c[t]%g;return n&&(c=[n].concat(c),++s),H(e,c,s)},C.precision=C.sd=function(e,t){var i,r,n,o=this;if(null!=e&&e!==!!e)return E(e,1,x),null==t?t=F:E(t,0,8),K(new Z(o),e,t);if(!(i=o.c))return null;if(r=(n=i.length-1)*b+1,n=i[n]){for(;n%10==0;n/=10,r--);for(n=i[0];n>=10;n/=10,r++);}return e&&o.e+1>r&&(r=o.e+1),r},C.shiftedBy=function(e){return E(e,-9007199254740991,w),this.times("1e"+e)},C.squareRoot=C.sqrt=function(){var e,t,r,n,o,s=this,c=s.c,a=s.s,l=s.e,u=M+4,p=new Z("0.5");if(1!==a||!c||!c[0])return new Z(!a||a<0&&(!c||c[0])?NaN:c?s:1/0);if(0==(a=Math.sqrt(+W(s)))||a==1/0?(((t=_(c)).length+l)%2==0&&(t+="0"),a=Math.sqrt(+t),l=S((l+1)/2)-(l<0||l%2),r=new Z(t=a==1/0?"5e"+l:(t=a.toExponential()).slice(0,t.indexOf("e")+1)+l)):r=new Z(a+""),r.c[0])for((a=(l=r.e)+u)<3&&(a=0);;)if(o=r,r=p.times(o.plus(i(s,o,u,1))),_(o.c).slice(0,a)===(t=_(r.c)).slice(0,a)){if(r.e<l&&--a,"9999"!=(t=t.slice(a-3,a+1))&&(n||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(K(r,r.e+M+2,1),e=!r.times(r).eq(s));break}if(!n&&(K(o,o.e+M+2,0),o.times(o).eq(s))){r=o;break}u+=4,a+=4,n=1}return K(r,r.e+M+1,F,e)},C.toExponential=function(e,t){return null!=e&&(E(e,0,x),e++),q(this,e,t,1)},C.toFixed=function(e,t){return null!=e&&(E(e,0,x),e=e+this.e+1),q(this,e,t)},C.toFormat=function(e,t,i){var r,n=this;if(null==i)null!=e&&t&&"object"==typeof t?(i=t,t=null):e&&"object"==typeof e?(i=e,e=t=null):i=G;else if("object"!=typeof i)throw Error(d+"Argument not an object: "+i);if(r=n.toFixed(e,t),n.c){var o,s=r.split("."),c=+i.groupSize,a=+i.secondaryGroupSize,l=i.groupSeparator||"",u=s[0],p=s[1],f=n.s<0,h=f?u.slice(1):u,m=h.length;if(a&&(o=c,c=a,a=o,m-=o),c>0&&m>0){for(o=m%c||c,u=h.substr(0,o);o<m;o+=c)u+=l+h.substr(o,c);a>0&&(u+=l+h.slice(o)),f&&(u="-"+u)}r=p?u+(i.decimalSeparator||"")+((a=+i.fractionGroupSize)?p.replace(new RegExp("\\d{"+a+"}\\B","g"),"$&"+(i.fractionGroupSeparator||"")):p):u}return(i.prefix||"")+r+(i.suffix||"")},C.toFraction=function(e){var t,r,n,o,s,c,a,l,u,p,f,h,m=this,g=m.c;if(null!=e&&(!(a=new Z(e)).isInteger()&&(a.c||1!==a.s)||a.lt(A)))throw Error(d+"Argument "+(a.isInteger()?"out of range: ":"not an integer: ")+W(a));if(!g)return new Z(m);for(t=new Z(A),u=r=new Z(A),n=l=new Z(A),h=_(g),s=t.e=h.length-m.e-1,t.c[0]=y[(c=s%b)<0?b+c:c],e=!e||a.comparedTo(t)>0?s>0?t:u:a,c=$,$=1/0,a=new Z(h),l.c[0]=0;p=i(a,t,0,1),1!=(o=r.plus(p.times(n))).comparedTo(e);)r=n,n=o,u=l.plus(p.times(o=u)),l=o,t=a.minus(p.times(o=t)),a=o;return o=i(e.minus(r),n,0,1),l=l.plus(o.times(u)),r=r.plus(o.times(n)),l.s=u.s=m.s,f=i(u,n,s*=2,F).minus(m).abs().comparedTo(i(l,r,s,F).minus(m).abs())<1?[u,n]:[l,r],$=c,f},C.toNumber=function(){return+W(this)},C.toPrecision=function(e,t){return null!=e&&E(e,1,x),q(this,e,t,2)},C.toString=function(e){var t,i=this,n=i.s,o=i.e;return null===o?n?(t="Infinity",n<0&&(t="-"+t)):t="NaN":(null==e?t=o<=R||o>=D?P(_(i.c),o):O(_(i.c),o,"0"):10===e&&z?t=O(_((i=K(new Z(i),M+o+1,F)).c),i.e,"0"):(E(e,2,L.length,"Base"),t=r(O(_(i.c),o,"0"),10,e,n,!0)),n<0&&i.c[0]&&(t="-"+t)),t},C.valueOf=C.toJSON=function(){return W(this)},C._isBigNumber=!0,C[Symbol.toStringTag]="BigNumber",C[Symbol.for("nodejs.util.inspect.custom")]=C.valueOf,null!=t&&Z.set(t),Z}();const C=T,A=/(?:(?!^-\d+))[^\d]+(?=.*[^\d])/g,M=/(?:(?!^-\d+))([^\d]+)/g,F=e=>{let t=e;const i=t.match(M);if(null===i)return t;if(i.length>1){const e=new Set(i);if(1===Array.from(e).length)return t.replace(M,"")}return t=t.replace(A,"").replace(M,"."),t};
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:R}=window;class D{constructor(e,t,i,r){this.object=e,this.modelKey=t,this.value=i,this.previousValue=r,this.propagationStopped=!1}stopPropagation(){this.propagationStopped=!0}isPropagationStopped(){return this.propagationStopped}}class B{constructor(e,t){e.length||console.error("Invalid empty form as input"),this.$form=e,this.fullModelMapping=t,this.model={},this.modelMapping={},this.modelMapping={},this.formMapping={},this.watchedProperties={},this.initFormMapping(),this.updateFullObject(),this.watchUpdates()}getModel(){return this.model}getInputsFor(e){if(!Object.prototype.hasOwnProperty.call(this.fullModelMapping,e))return;let t=this.fullModelMapping[e];Array.isArray(t)||(t=[t]);const i=[],r=this.$form.get(0);return r?(t.forEach((e=>{const t=r.querySelectorAll(`[name="${e}"]`);t.length&&t.forEach((e=>{i.push(e)}))})),i.length?R(i):void 0):void 0}set(e,t){Object.prototype.hasOwnProperty.call(this.modelMapping,e)&&t!==this.getValue(e)&&(this.updateInputValue(e,t),this.updateObjectByKey(e,t))}watch(e,t){(Array.isArray(e)?e:[e]).forEach((e=>{Object.prototype.hasOwnProperty.call(this.watchedProperties,e)||(this.watchedProperties[e]=[]),this.watchedProperties[e].push(t)}))}getBigNumber(e){const t=this.getValue(e);return void 0===t?void 0:new C(F(t))}getValue(e){const t=e.split(".");return R.serializeJSON.deepGet(this.model,t)}updateFullObject(){const e=this.$form.find(":input:disabled").removeAttr("disabled"),t=this.$form.serializeArray();e.prop("disabled",!0);const i={};t.forEach((e=>{i[e.name]=e.value})),this.model={},Object.keys(this.modelMapping).forEach((e=>{const t=this.modelMapping[e],r=i[t];this.updateObjectByKey(e,r)}))}watchUpdates(){this.$form.on("change dp.change",":input",(e=>this.inputUpdated(e)))}inputUpdated(e){const t=e.currentTarget;if(!Object.prototype.hasOwnProperty.call(this.formMapping,t.name))return;const i=this.getInputValue(R(t)),r=this.formMapping[t.name];this.updateInputValue(r,i,t.name),this.updateObjectByKey(r,i)}getInputValue(e){return e.is(":checkbox")?e.is(":checked"):e.val()}updateInputValue(e,t,i){const r=this.fullModelMapping[e];Array.isArray(r)?r.forEach((e=>{i!==e&&this.updateInputByName(e,t)})):i!==r&&this.updateInputByName(r,t)}updateInputByName(e,t){const i=R(`[name="${e}"]`,this.$form);i.length?this.hasSameValue(this.getInputValue(i),t)||(i.is(":checkbox")?(i.val(t?1:0),i.prop("checked",!!t)):i.val(t),"select2"===i.data("toggle")&&i.trigger("change"),this.triggerChangeEvent(e)):console.error(`Input with name ${e} is not present in form.`)}triggerChangeEvent(e){const t=document.querySelector(`[name="${e}"]`);if(!t)return;const i=document.createEvent("HTMLEvents");i.initEvent("change",!1,!0),t.dispatchEvent(i)}hasSameValue(e,t){if("boolean"==typeof e||"boolean"==typeof t)return e===t;const i=new C(F(t));return!!new C(F(e)).isEqualTo(i)||t==e}updateObjectByKey(e,t){const i=e.split("."),r=R.serializeJSON.deepGet(this.model,i);if(r===t)return;R.serializeJSON.deepSet(this.model,i,t);const n=new D(this.model,e,t,r);if(Object.prototype.hasOwnProperty.call(this.watchedProperties,e)){this.watchedProperties[e].forEach((e=>{n.isPropagationStopped()||e(n)}))}}initFormMapping(){Object.keys(this.fullModelMapping).forEach((e=>{const t=this.fullModelMapping[e];Array.isArray(t)?t.forEach((t=>{this.addFormMapping(t,e)})):this.addFormMapping(t,e)}))}addFormMapping(e,t){Object.prototype.hasOwnProperty.call(this.formMapping,e)?console.error(`The form element ${e} is already mapped to ${this.formMapping[e]}`):(this.formMapping[e]=t,this.modelMapping[t]=e)}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const $={"impact.priceTaxExcluded":"combination_form[price_impact][price_tax_excluded]","impact.priceTaxIncluded":"combination_form[price_impact][price_tax_included]","impact.unitPriceTaxExcluded":"combination_form[price_impact][unit_price_tax_excluded]","impact.unitPriceTaxIncluded":"combination_form[price_impact][unit_price_tax_included]","price.ecotaxTaxExcluded":"combination_form[price_impact][ecotax_tax_excluded]","price.ecotaxTaxIncluded":"combination_form[price_impact][ecotax_tax_included]","price.wholesalePrice":"combination_form[price_impact][wholesale_price]","price.finalPriceTaxExcluded":"combination_form[price_impact][final_price_tax_excluded]","price.finalPriceTaxIncluded":"combination_form[price_impact][final_price_tax_included]","product.priceTaxExcluded":"combination_form[price_impact][product_price_tax_excluded]","product.taxRate":"combination_form[price_impact][product_tax_rate]","product.ecotaxTaxExcluded":"combination_form[price_impact][product_ecotax_tax_excluded]","suppliers.defaultSupplierId":"combination_form[default_supplier_id]"};var k=i(4431),j=i.n(k),U=i(1527);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class G{constructor(e,t){this.eventEmitter=t,this.mapper=new B(e,$);const i=this.mapper.getInputsFor("impact.priceTaxExcluded");this.precision=i.data("displayPricePrecision"),this.numberFormatter=U.NumberFormatter.build(null==i?void 0:i.data("priceSpecification"));this.mapper.watch(["impact.priceTaxExcluded","impact.priceTaxIncluded","impact.unitPriceTaxExcluded","impact.unitPriceTaxIncluded","price.ecotaxTaxExcluded","price.ecotaxTaxIncluded","price.wholesalePrice"],(e=>this.updateCombinationPrices(e))),this.updateFinalPrices()}getCombination(){return this.mapper.getModel()}watch(e,t){this.mapper.watch(e,t)}set(e,t){this.mapper.set(e,t)}displayPrice(e){return this.numberFormatter.format(e.toNumber())}updateCombinationPrices(e){var t,i,r,n,o,s;if(new(j())(e.value).isNaN())return e.stopPropagation(),void this.mapper.set(e.modelKey,new(j())(0).toFixed(this.precision));const c=this.getTaxRatio();if(!c.isNaN()){switch(e.modelKey){case"impact.priceTaxIncluded":{const e=null!=(t=this.mapper.getBigNumber("impact.priceTaxIncluded"))?t:new(j())(0);this.mapper.set("impact.priceTaxExcluded",e.dividedBy(c).toFixed(this.precision));break}case"impact.priceTaxExcluded":{const e=null!=(i=this.mapper.getBigNumber("impact.priceTaxExcluded"))?i:new(j())(0);this.mapper.set("impact.priceTaxIncluded",e.times(c).toFixed(this.precision));break}case"price.ecotaxTaxIncluded":{const e=this.getEcoTaxRatio(),t=null!=(r=this.mapper.getBigNumber("price.ecotaxTaxIncluded"))?r:new(j())(0);this.mapper.set("price.ecotaxTaxExcluded",t.dividedBy(e).toFixed(this.precision));break}case"price.ecotaxTaxExcluded":{const e=this.getEcoTaxRatio(),t=(null!=(n=this.mapper.getBigNumber("price.ecotaxTaxExcluded"))?n:new(j())(0)).times(e),i=this.getEcotaxTaxIncluded(t);this.updateImpactForEcotax(i),this.mapper.set("price.ecotaxTaxIncluded",t.toFixed(this.precision));break}case"impact.unitPriceTaxIncluded":{const e=null!=(o=this.mapper.getBigNumber("impact.unitPriceTaxIncluded"))?o:new(j())(0);this.mapper.set("impact.unitPriceTaxExcluded",e.dividedBy(c).toFixed(this.precision));break}case"impact.unitPriceTaxExcluded":{const e=null!=(s=this.mapper.getBigNumber("impact.unitPriceTaxExcluded"))?s:new(j())(0);this.mapper.set("impact.unitPriceTaxIncluded",e.times(c).toFixed(this.precision));break}}this.updateFinalPrices()}}updateImpactForEcotax(e){var t,i;const r=this.getTaxRatio(),n=null!=(t=this.mapper.getBigNumber("price.finalPriceTaxIncluded"))?t:new(j())(0),o=(null!=(i=this.mapper.getBigNumber("product.priceTaxExcluded"))?i:new(j())(0)).times(r),s=n.minus(e).minus(o);this.mapper.set("impact.priceTaxExcluded",s.dividedBy(r).toFixed(this.precision))}updateFinalPrices(){var e,t;const i=this.getTaxRatio(),r=this.getEcoTaxRatio();let n=null!=(e=this.mapper.getBigNumber("product.priceTaxExcluded"))?e:new(j())(0),o=null!=(t=this.mapper.getBigNumber("impact.priceTaxExcluded"))?t:new(j())(0),s=this.getEcotaxTaxExcluded();n.isNaN()&&(n=new(j())(0)),o.isNaN()&&(o=new(j())(0)),s.isNaN()&&(s=new(j())(0));const c=s.times(r),a=n.plus(o),l=a.plus(s),u=a.times(i).plus(c);this.mapper.set("price.finalPriceTaxExcluded",l.toFixed(this.precision)),this.mapper.set("price.finalPriceTaxIncluded",u.toFixed(this.precision));const p=this.mapper.getInputsFor("price.finalPriceTaxExcluded"),f=this.mapper.getInputsFor("price.finalPriceTaxIncluded");p&&p.siblings(".final-price-preview").text(this.displayPrice(l)),f&&f.siblings(".final-price-preview").text(this.displayPrice(u))}getEcotaxTaxExcluded(){var e,t;const i=null!=(e=this.mapper.getBigNumber("price.ecotaxTaxExcluded"))?e:new(j())(0);return i.isNegative()||i.isZero()?null!=(t=this.mapper.getBigNumber("product.ecotaxTaxExcluded"))?t:new(j())(0):i}getEcotaxTaxIncluded(e){var t;if(!e.isNegative()&&!e.isZero())return e;const i=null!=(t=this.mapper.getBigNumber("product.ecotaxTaxExcluded"))?t:new(j())(0),r=this.getEcoTaxRatio();return i.times(r)}getTaxRatio(){var e;let t=null!=(e=this.mapper.getBigNumber("product.taxRate"))?e:new(j())(0);return t.isNaN()&&(t=new(j())(0)),t.dividedBy(100).plus(1)}getEcoTaxRatio(){const e=this.mapper.getInputsFor("price.ecotaxTaxExcluded");if(!e)return new(j())(1);let t;try{t=new(j())(e.data("taxRate"))}catch(e){t=new(j())(NaN)}return t.isNaN()&&(t=new(j())(0)),t.dividedBy(100).plus(1)}}var L=i(9567);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */class z{constructor(e,t,i){this.defaultSupplierId=t,this.wholesalePrice=i,this.map=(e=>{const t=(t,i)=>`${e}_${t}_${i}`;return{productSuppliersCollection:`${e}`,productSuppliersCollectionRow:".product-suppliers-collection-row",productSuppliersTable:`${e} table`,productsSuppliersTableBody:`${e} table tbody`,productsSuppliersRows:`${e} table tbody .product_supplier_row`,productsSupplierRowSelector:".product_supplier_row",productSupplierRow:{supplierIdInput:e=>t(e,"supplier_id"),supplierNameInput:e=>t(e,"supplier_name"),productSupplierIdInput:e=>t(e,"product_supplier_id"),referenceInput:e=>t(e,"reference"),priceInput:e=>t(e,"price_tax_excluded"),currencyIdInput:e=>t(e,"currency_id"),supplierNamePreview:e=>`#product_supplier_row_${e} .supplier_name .preview`,currencySymbol:e=>`#product_supplier_row_${e} .money-type .input-group-text`}}})(e),this.$productSuppliersCollection=L(this.map.productSuppliersCollection),this.$collectionRow=this.$productSuppliersCollection.parents(this.map.productSuppliersCollectionRow),this.$productsTable=L(this.map.productSuppliersTable),this.$productsTableBody=L(this.map.productsSuppliersTableBody),this.selectedSuppliers=[],this.productSuppliers={},this.prototypeTemplate=this.$productSuppliersCollection.data("prototype"),this.prototypeName=this.$productSuppliersCollection.data("prototypeName"),this.baseDataForSupplier=this.getBaseDataForSupplier(),this.init()}setSelectedSuppliers(e){this.selectedSuppliers=e;const t=[];this.selectedSuppliers.forEach((e=>{t.push(e.supplierId),this.addSupplier(e)}));Object.keys(this.productSuppliers).forEach((e=>{t.includes(e)||this.removeSupplier(e)})),this.renderSuppliers(),this.memorizeCurrentSuppliers(),this.toggleRowVisibility()}setDefaultSupplierId(e){this.defaultSupplierId=e,this.selectedSuppliers.forEach((t=>{t.isDefault=t.supplierId===e})),this.memorizeCurrentSuppliers()}init(){this.memorizeCurrentSuppliers(),this.selectedSuppliers=this.getSuppliersFromTable(),this.toggleRowVisibility(),this.$productsTable.on("change",":input",(()=>{this.memorizeCurrentSuppliers()})),this.$productsTable.on("change",'select[name$="[currency_id]"]',(e=>{var t;const i=null==(t=L(e.target).find(":selected"))?void 0:t.attr("symbol");L(e.target).parents(this.map.productsSupplierRowSelector).find(".money-type .input-group-prepend .input-group-text").html(i)}))}addSupplier(e){const t=this.getDefaultProductSupplier(),i=(null==t?void 0:t.price)||this.wholesalePrice;if(void 0===this.productSuppliers[e.supplierId]){const t=Object.create(this.baseDataForSupplier);t.supplierId=e.supplierId,t.supplierName=e.supplierName,t.price=i,this.productSuppliers[e.supplierId]=t}else{const t=this.productSuppliers[e.supplierId];t.removed&&(t.removed=!1,t.price=i)}}removeSupplier(e){Object.prototype.hasOwnProperty.call(this.productSuppliers,e)&&(this.productSuppliers[e].removed=!0)}memorizeCurrentSuppliers(){const e=document.querySelectorAll(this.map.productsSuppliersRows);e.length&&e.forEach((e=>{const t=e.dataset.supplierIndex,i=L(this.map.productSupplierRow.supplierIdInput(t)).val();this.productSuppliers[i]={supplierId:i,productSupplierId:L(this.map.productSupplierRow.productSupplierIdInput(t)).val(),supplierName:L(this.map.productSupplierRow.supplierNameInput(t)).val(),reference:L(this.map.productSupplierRow.referenceInput(t)).val(),price:L(this.map.productSupplierRow.priceInput(t)).val(),currencyId:L(this.map.productSupplierRow.currencyIdInput(t)).val(),isDefault:i===this.defaultSupplierId,removed:!1}}))}getSuppliersFromTable(){const e=[],t=document.querySelectorAll(this.map.productsSuppliersRows);return t.length?(t.forEach((t=>{const i=t.dataset.supplierIndex,r=L(this.map.productSupplierRow.supplierIdInput(i)).val();e.push({supplierId:r,supplierName:L(this.map.productSupplierRow.supplierNameInput(i)).val(),isDefault:r===this.defaultSupplierId})})),e):e}renderSuppliers(){this.$productsTableBody.empty(),this.selectedSuppliers.forEach((e=>{var t;const i=this.productSuppliers[e.supplierId];if(i.removed)return;const r=this.prototypeTemplate.replace(new RegExp(this.prototypeName,"g"),i.supplierId);this.$productsTableBody.append(r);const n=this.map.productSupplierRow;L(n.supplierIdInput(i.supplierId)).val(i.supplierId),L(n.supplierNamePreview(i.supplierId)).html(i.supplierName),L(n.supplierNameInput(i.supplierId)).val(i.supplierName),L(n.productSupplierIdInput(i.supplierId)).val(i.productSupplierId),L(n.referenceInput(i.supplierId)).val(i.reference),L(n.priceInput(i.supplierId)).val(i.price),L(n.currencyIdInput(i.supplierId)).val(i.currencyId);const o=null==(t=L(n.currencyIdInput(i.supplierId)).find(":selected"))?void 0:t.attr("symbol");o&&L(n.currencySymbol(i.supplierId)).html(o)}))}toggleRowVisibility(){0!==this.selectedSuppliers.length?this.showCollectionRow():this.hideCollectionRow()}showCollectionRow(){this.$collectionRow.removeClass("d-none")}hideCollectionRow(){this.$collectionRow.addClass("d-none")}getBaseDataForSupplier(){const e=(new DOMParser).parseFromString(this.prototypeTemplate,"text/html");return{removed:!1,productSupplierId:this.extractFromPrototype(this.map.productSupplierRow.productSupplierIdInput,e),reference:this.extractFromPrototype(this.map.productSupplierRow.referenceInput,e),price:this.extractFromPrototype(this.map.productSupplierRow.priceInput,e),currencyId:this.extractFromPrototype(this.map.productSupplierRow.currencyIdInput,e),isDefault:!1}}extractFromPrototype(e,t){var i;const r=t.querySelector(e(this.prototypeName));return null!=(i=null==r?void 0:r.value)?i:null}getDefaultProductSupplier(){return Object.values(this.productSuppliers).find((e=>e.isDefault))}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:Z}=window;Z((()=>{window.prestashop.component.initComponents(["TranslatableField","TinyMCEEditor","TranslatableInput","EventEmitter","TextWithLengthCounter","DeltaQuantityInput","DisablingSwitch","ModifyAllShopsCheckbox"]);const e=Z(l),{eventEmitter:t}=window.prestashop.instance,i=new G(e,t);new z(u.productSuppliers,i.getCombination().suppliers.defaultSupplierId,i.getCombination().price.wholesalePrice),new a}))})(),window.combination_form=r})();