(()=>{"use strict";var e={9567:e=>{e.exports=window.jQuery}},t={};function o(i){var n=t[i];if(void 0!==n)return n.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,o),r.exports}o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{o.r(i);var e=o(9567),t=Object.defineProperty,n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,l=(e,o,i)=>o in e?t(e,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[o]=i,a=(e,t)=>{for(var o in t||(t={}))r.call(t,o)&&l(e,o,t[o]);if(n)for(var o of n(t))s.call(t,o)&&l(e,o,t[o]);return e};
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class c{constructor(e){const t=a({id:"confirm-modal",closable:!1},e);this.buildModalContainer(t)}buildModalContainer(e){this.container=document.createElement("div"),this.container.classList.add("modal","fade"),this.container.id=e.id,this.dialog=document.createElement("div"),this.dialog.classList.add("modal-dialog"),e.dialogStyle&&Object.keys(e.dialogStyle).forEach((t=>{this.dialog.style[t]=e.dialogStyle[t]})),this.content=document.createElement("div"),this.content.classList.add("modal-content"),this.message=document.createElement("p"),this.message.classList.add("modal-message"),this.header=document.createElement("div"),this.header.classList.add("modal-header"),e.modalTitle&&(this.title=document.createElement("h4"),this.title.classList.add("modal-title"),this.title.innerHTML=e.modalTitle),this.closeIcon=document.createElement("button"),this.closeIcon.classList.add("close"),this.closeIcon.setAttribute("type","button"),this.closeIcon.dataset.dismiss="modal",this.closeIcon.innerHTML="×",this.body=document.createElement("div"),this.body.classList.add("modal-body","text-left","font-weight-normal"),this.title&&this.header.appendChild(this.title),this.header.appendChild(this.closeIcon),this.content.append(this.header,this.body),this.body.appendChild(this.message),this.dialog.appendChild(this.content),this.container.appendChild(this.dialog)}}class d{constructor(e){const t=a({id:"confirm-modal",closable:!1,dialogStyle:{}},e);this.initContainer(t)}initContainer(t){this.modal||(this.modal=new c(t)),this.$modal=e(this.modal.container);const{id:o,closable:i}=t;this.$modal.modal({backdrop:!!i||"static",keyboard:void 0===i||i,show:!1}),this.$modal.on("hidden.bs.modal",(()=>{const e=document.querySelector(`#${o}`);e&&e.remove(),t.closeCallback&&t.closeCallback()})),document.body.appendChild(this.modal.container)}setTitle(e){return this.modal.title||(this.modal.title=document.createElement("h4"),this.modal.title.classList.add("modal-title"),this.modal.closeIcon?this.modal.header.insertBefore(this.modal.title,this.modal.closeIcon):this.modal.header.appendChild(this.modal.title)),this.modal.title.innerHTML=e,this}render(e){return this.modal.message.innerHTML=e,this}show(){return this.$modal.modal("show"),this}hide(){return this.$modal.modal("hide"),this.$modal.on("shown.bs.modal",(()=>{this.$modal.modal("hide"),this.$modal.off("shown.bs.modal")})),this}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
function u(e){return void 0===e}var h=Object.defineProperty,m=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,g=(e,t,o)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class b extends c{constructor(e){super(e)}buildModalContainer(e){super.buildModalContainer(e),this.message.classList.add("confirm-message"),this.message.innerHTML=e.confirmMessage,this.footer=document.createElement("div"),this.footer.classList.add("modal-footer"),this.closeButton=document.createElement("button"),this.closeButton.setAttribute("type","button"),this.closeButton.classList.add("btn","btn-outline-secondary","btn-lg"),this.closeButton.dataset.dismiss="modal",this.closeButton.innerHTML=e.closeButtonLabel,this.confirmButton=document.createElement("button"),this.confirmButton.setAttribute("type","button"),this.confirmButton.classList.add("btn",e.confirmButtonClass,"btn-lg","btn-confirm-submit"),this.confirmButton.dataset.dismiss="modal",this.confirmButton.innerHTML=e.confirmButtonLabel,this.footer.append(this.closeButton,...e.customButtons,this.confirmButton),this.content.append(this.footer)}}class S extends d{constructor(e,t,o){var i;let n;n=u(e.confirmCallback)?u(t)?()=>{console.error("No confirm callback provided for ConfirmModal component.")}:t:e.confirmCallback;super(((e,t)=>{for(var o in t||(t={}))p.call(t,o)&&g(e,o,t[o]);if(m)for(var o of m(t))f.call(t,o)&&g(e,o,t[o]);return e})({id:"confirm-modal",confirmMessage:"Are you sure?",closeButtonLabel:"Close",confirmButtonLabel:"Accept",confirmButtonClass:"btn-primary",customButtons:[],closable:!1,modalTitle:e.confirmTitle,dialogStyle:{},confirmCallback:n,closeCallback:null!=(i=e.closeCallback)?i:o},e))}initContainer(e){this.modal=new b(e),this.modal.confirmButton.addEventListener("click",e.confirmCallback),super.initContainer(e)}}var y=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var o=-1;return e.some((function(e,i){return e[0]===t&&(o=i,!0)})),o}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var o=e(this.__entries__,t),i=this.__entries__[o];return i&&i[1]},t.prototype.set=function(t,o){var i=e(this.__entries__,t);~i?this.__entries__[i][1]=o:this.__entries__.push([t,o])},t.prototype.delete=function(t){var o=this.__entries__,i=e(o,t);~i&&o.splice(i,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var o=0,i=this.__entries__;o<i.length;o++){var n=i[o];e.call(t,n[1],n[0])}},t}()}(),v="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,w=void 0!==o.g&&o.g.Math===Math?o.g:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),_="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(w):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)};var M=["top","right","bottom","left","width","height","size","weight"],C="undefined"!=typeof MutationObserver,k=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=function(e,t){var o=!1,i=!1,n=0;function r(){o&&(o=!1,e()),i&&l()}function s(){_(r)}function l(){var e=Date.now();if(o){if(e-n<2)return;i=!0}else o=!0,i=!1,setTimeout(s,t);n=e}return l}(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,o=t.indexOf(e);~o&&t.splice(o,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){v&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),C?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){v&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,o=void 0===t?"":t;M.some((function(e){return!!~o.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),L=function(e,t){for(var o=0,i=Object.keys(t);o<i.length;o++){var n=i[o];Object.defineProperty(e,n,{value:t[n],enumerable:!1,writable:!1,configurable:!0})}return e},A=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||w},I=B(0,0,0,0);function E(e){return parseFloat(e)||0}function D(e){for(var t=[],o=1;o<arguments.length;o++)t[o-1]=arguments[o];return t.reduce((function(t,o){return t+E(e["border-"+o+"-width"])}),0)}function T(e){var t=e.clientWidth,o=e.clientHeight;if(!t&&!o)return I;var i=A(e).getComputedStyle(e),n=function(e){for(var t={},o=0,i=["top","right","bottom","left"];o<i.length;o++){var n=i[o],r=e["padding-"+n];t[n]=E(r)}return t}(i),r=n.left+n.right,s=n.top+n.bottom,l=E(i.width),a=E(i.height);if("border-box"===i.boxSizing&&(Math.round(l+r)!==t&&(l-=D(i,"left","right")+r),Math.round(a+s)!==o&&(a-=D(i,"top","bottom")+s)),!function(e){return e===A(e).document.documentElement}(e)){var c=Math.round(l+r)-t,d=Math.round(a+s)-o;1!==Math.abs(c)&&(l-=c),1!==Math.abs(d)&&(a-=d)}return B(n.left,n.top,l,a)}var O="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof A(e).SVGGraphicsElement}:function(e){return e instanceof A(e).SVGElement&&"function"==typeof e.getBBox};function R(e){return v?O(e)?function(e){var t=e.getBBox();return B(0,0,t.width,t.height)}(e):T(e):I}function B(e,t,o,i){return{x:e,y:t,width:o,height:i}}var x=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=B(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=R(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),P=function(e,t){var o,i,n,r,s,l,a,c=(i=(o=t).x,n=o.y,r=o.width,s=o.height,l="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,a=Object.create(l.prototype),L(a,{x:i,y:n,width:r,height:s,top:n,right:i+r,bottom:s+n,left:i}),a);L(this,{target:e,contentRect:c})},U=function(){function e(e,t,o){if(this.activeObservations_=[],this.observations_=new y,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=o}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof A(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new x(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof A(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new P(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),$="undefined"!=typeof WeakMap?new WeakMap:new y,j=function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var o=k.getInstance(),i=new U(t,o,this);$.set(this,i)};["observe","unobserve","disconnect"].forEach((function(e){j.prototype[e]=function(){var t;return(t=$.get(this))[e].apply(t,arguments)}}));void 0!==w.ResizeObserver&&w.ResizeObserver;const G=class extends Event{constructor(e,t={}){super(G.parentWindowEvent),this.eventName=e,this.eventParameters=t}get name(){return this.eventName}get parameters(){return this.eventParameters}};G.parentWindowEvent="IframeClientEvent";Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;Object.defineProperty,Object.getOwnPropertySymbols,Object.prototype.hasOwnProperty,Object.prototype.propertyIsEnumerable;
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const F=S,N={moduleItemList:e=>`div.module-item-list[data-tech-name='${e}']`,moduleItem:e=>`.module-item[data-tech-name='${e}']`},q=e=>`#${e}`;var Y=o(9567);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const z=N,{$:V}=window;class H{constructor(){this.pendingRequest=!1,this.moduleActionMenuLinkSelector="button.module_action_menu_",this.moduleActionMenuInstallLinkSelector="button.module_action_menu_install",this.moduleActionMenuEnableLinkSelector="button.module_action_menu_enable",this.moduleActionMenuUninstallLinkSelector="button.module_action_menu_uninstall",this.moduleActionMenuDisableLinkSelector="button.module_action_menu_disable",this.moduleActionMenuEnableMobileLinkSelector="button.module_action_menu_enableMobile",this.moduleActionMenuDisableMobileLinkSelector="button.module_action_menu_disableMobile",this.moduleActionMenuResetLinkSelector="button.module_action_menu_reset",this.moduleActionMenuUpdateLinkSelector="button.module_action_menu_upgrade",this.moduleActionMenuDeleteLinkSelector="button.module_action_menu_delete",this.moduleItemListSelector=".module-item-list",this.moduleItemGridSelector=".module-item-grid",this.moduleItemActionsSelector=".module-actions",this.moduleActionModalDisableLinkSelector="a.module_action_modal_disable",this.moduleActionModalResetLinkSelector="a.module_action_modal_reset",this.moduleActionModalUninstallLinkSelector="a.module_action_modal_uninstall",this.forceDeletionOption="#force_deletion",this.eventEmitter=window.prestashop.component.EventEmitter,this.initActionButtons()}initActionButtons(){const e=this;V(document).on("click",this.forceDeletionOption,(function(){const t=V(e.moduleActionModalUninstallLinkSelector,V(z.moduleItemList(V(this).attr("data-tech-name"))));!0===V(this).prop("checked")?t.attr("data-deletion","true"):t.removeAttr("data-deletion")})),V(document).on("click",this.moduleActionMenuInstallLinkSelector,(function(){return e.dispatchPreEvent("install",this)&&e.confirmAction("install",this)&&e.requestToController("install",V(this))})),V(document).on("click",this.moduleActionMenuEnableLinkSelector,(function(){return e.dispatchPreEvent("enable",this)&&e.confirmAction("enable",this)&&e.requestToController("enable",V(this))})),V(document).on("click",this.moduleActionMenuUninstallLinkSelector,(function(){return e.dispatchPreEvent("uninstall",this)&&e.confirmAction("uninstall",this)&&e.requestToController("uninstall",V(this))})),V(document).on("click",this.moduleActionMenuDeleteLinkSelector,(function(){return e.dispatchPreEvent("delete",this)&&e.confirmAction("delete",this)&&e.requestToController("delete",V(this))})),V(document).on("click",this.moduleActionMenuDisableLinkSelector,(function(){return e.dispatchPreEvent("disable",this)&&e.confirmAction("disable",this)&&e.requestToController("disable",V(this))})),V(document).on("click",this.moduleActionMenuEnableMobileLinkSelector,(function(){return e.dispatchPreEvent("enableMobile",this)&&e.confirmAction("enableMobile",this)&&e.requestToController("enableMobile",V(this))})),V(document).on("click",this.moduleActionMenuDisableMobileLinkSelector,(function(){return e.dispatchPreEvent("disableMobile",this)&&e.confirmAction("disableMobile",this)&&e.requestToController("disableMobile",V(this))})),V(document).on("click",this.moduleActionMenuResetLinkSelector,(function(){return e.dispatchPreEvent("reset",this)&&e.confirmAction("reset",this)&&e.requestToController("reset",V(this))})),V(document).on("click",this.moduleActionMenuUpdateLinkSelector,(function(t){t.preventDefault();const o=V(`#${V(this).data("confirm_modal")}`),i=window.isShopMaintenance;if(1===o.length)return e.dispatchPreEvent("update",this)&&e.confirmAction("update",this)&&e.requestToController("update",V(this));{const t=document.createElement("a");t.classList.add("btn","btn-primary","btn-lg"),t.setAttribute("href",window.moduleURLs.maintenancePage),t.innerHTML=window.moduleTranslations.moduleModalUpdateMaintenance;new F({id:"confirm-module-update-modal",confirmTitle:window.moduleTranslations.singleModuleModalUpdateTitle,closeButtonLabel:window.moduleTranslations.moduleModalUpdateCancel,confirmButtonLabel:i?window.moduleTranslations.moduleModalUpdateUpgrade:window.moduleTranslations.upgradeAnywayButtonText,confirmButtonClass:i?"btn-primary":"btn-secondary",confirmMessage:i?"":window.moduleTranslations.moduleModalUpdateConfirmMessage,closable:!0,customButtons:i?[]:[t]},(()=>e.dispatchPreEvent("update",this)&&e.confirmAction("update",this)&&e.requestToController("update",V(this)))).show()}return!1})),V(document).on("click",this.moduleActionModalDisableLinkSelector,(function(){return e.requestToController("disable",V(e.moduleActionMenuDisableLinkSelector,V(z.moduleItemList(V(this).attr("data-tech-name")))))})),V(document).on("click",this.moduleActionModalResetLinkSelector,(function(){return e.requestToController("reset",V(e.moduleActionMenuResetLinkSelector,V(z.moduleItemList(V(this).attr("data-tech-name")))))})),V(document).on("click",this.moduleActionModalUninstallLinkSelector,(t=>{V(t.target).parents(".modal").on("hidden.bs.modal",(()=>e.requestToController("uninstall",V(e.moduleActionMenuUninstallLinkSelector,V(z.moduleItemList(V(t.target).attr("data-tech-name")))),V(t.target).attr("data-deletion"))))}))}getModuleItemSelector(){return V(this.moduleItemListSelector).length?this.moduleItemListSelector:this.moduleItemGridSelector}confirmAction(e,t){const o=V(q(V(t).data("confirm_modal")));return 1!==o.length||(o.first().modal("show"),!1)}dispatchPreEvent(e,t){const o=Y.Event("module_card_action_event");return V(t).trigger(o,[e]),!1===o.isPropagationStopped()&&!1===o.isImmediatePropagationStopped()&&!1!==o.result}hasPendingRequest(){return this.pendingRequest}requestToController(e,t,o=!1,i=(()=>!0)){if(this.pendingRequest)return V.growl.warning({message:window.translate_javascripts["An action is already in progress. Please wait for it to finish."]}),!1;this.pendingRequest=!0;const n=this;let r=t.closest(this.moduleItemActionsSelector);const s=t.closest("form"),l=V('<button class="btn-primary-reverse onclick unbind spinner "></button>'),a=`//${window.location.host}${s.attr("action")}`,c=s.serializeArray();let d=!1;return"true"!==o&&!0!==o||c.push({name:"actionParams[deletion]",value:"true"}),V.ajax({url:a,dataType:"json",method:"POST",data:c,beforeSend(){r.hide(),r.after(l)}}).done((t=>{if(void 0===t)return void V.growl.error({message:"No answer received from server",fixed:!0});if(void 0!==t.status&&!1===t.status)return void V.growl.error({message:t.msg,fixed:!0});const i=Object.keys(t)[0];if(!1===t[i].status)return void V.growl.error({message:t[i].msg,fixed:!0});if(V.growl({message:t[i].msg,duration:6e3}),!0===t[i].refresh_needed)return void(d=!0);const s=n.getModuleItemSelector().replace(".","");let l=null;"delete"!==e||t[i].has_download_url?"uninstall"===e?(l=r.closest(`.${s}`),l.attr("data-installed","0"),l.attr("data-active","0"),"true"!==o&&!0!==o||t[i].has_download_url?this.eventEmitter.emit("Module Uninstalled",l):this.eventEmitter.emit("Module Delete",l)):"disable"===e?(l=r.closest(`.${s}`),l.addClass(`${s}-isNotActive`),l.attr("data-active","0"),this.eventEmitter.emit("Module Disabled",l)):"enable"===e?(l=r.closest(`.${s}`),l.removeClass(`${s}-isNotActive`),l.attr("data-active","1"),this.eventEmitter.emit("Module Enabled",l)):"install"===e?(l=r.closest(`.${s}`),l.attr("data-installed","1"),l.attr("data-active","1"),l.removeClass(`${s}-isNotActive`),this.eventEmitter.emit("Module Installed",l)):"update"!==e&&"upgrade"!==e||(l=r.closest(`.${s}`),this.eventEmitter.emit("Module Upgraded",l)):(l=r.closest(`.${s}`),this.eventEmitter.emit("Module Delete",l)),r=V(t[i].action_menu_html).replaceAll(r),r.hide()})).fail((()=>{const t=r.closest("module-item-list").data("techName");V.growl.error({message:`Could not perform action ${e} for module ${t}`,fixed:!0})})).always((()=>{d?document.location.reload():(r.fadeIn(),l.remove(),this.pendingRequest=!1,i&&i())})),!1}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:Z}=window;const W=class{constructor(e){this.eventEmitter=window.prestashop.component.EventEmitter,this.moduleCardController=e,this.DEFAULT_MAX_RECENTLY_USED=10,this.DEFAULT_MAX_PER_CATEGORIES=6,this.DISPLAY_GRID="grid",this.DISPLAY_LIST="list",this.CATEGORY_RECENTLY_USED="recently-used",this.currentCategoryDisplay={},this.currentDisplay="",this.isCategoryGridDisplayed=!1,this.currentTagsList=[],this.currentRefCategory=null,this.currentRefStatus=null,this.currentSorting=null,this.pstaggerInput=null,this.lastBulkAction=null,this.isUploadStarted=!1,this.findModuleUsed=!1,this.recentlyUsedSelector="#module-recently-used-list .modules-list",this.modulesList=[],this.moduleShortList=".module-short-list",this.seeMoreSelector=".see-more",this.seeLessSelector=".see-less",this.moduleItemGridSelector=".module-item-grid",this.moduleItemListSelector=".module-item-list",this.categorySelectorLabelSelector=".module-category-selector-label",this.categorySelector=".module-category-selector",this.categoryItemSelector=".module-category-menu",this.categoryResetBtnSelector=".module-category-reset",this.moduleInstallBtnSelector="input.module-install-btn",this.moduleSortingDropdownSelector=".module-sorting-author select",this.categoryGridSelector="#modules-categories-grid",this.categoryGridItemSelector=".module-category-item",this.upgradeAllSource=".module_action_menu_upgrade_all",this.upgradeContainer="#modules-list-container-update",this.upgradeAllTargets=`${this.upgradeContainer} .module_action_menu_upgrade:visible`,this.notificationContainer="#modules-list-container-notification",this.bulkActionDropDownSelector=".module-bulk-actions",this.bulkItemSelector=".module-bulk-menu",this.bulkActionCheckboxListSelector=".module-checkbox-bulk-list input",this.bulkActionCheckboxGridSelector=".module-checkbox-bulk-grid input",this.checkedBulkActionListSelector=`${this.bulkActionCheckboxListSelector}:checked`,this.checkedBulkActionGridSelector=`${this.bulkActionCheckboxGridSelector}:checked`,this.bulkActionCheckboxSelector="#module-modal-bulk-checkbox",this.bulkConfirmModalSelector="#module-modal-bulk-confirm",this.bulkConfirmModalActionNameSelector="#module-modal-bulk-confirm-action-name",this.bulkConfirmModalListSelector="#module-modal-bulk-confirm-list",this.bulkConfirmModalAckBtnSelector="#module-modal-confirm-bulk-ack",this.placeholderGlobalSelector=".module-placeholders-wrapper",this.placeholderFailureGlobalSelector=".module-placeholders-failure",this.placeholderFailureMsgSelector=".module-placeholders-failure-msg",this.placeholderFailureRetryBtnSelector="#module-placeholders-failure-retry",this.statusSelectorLabelSelector=".module-status-selector-label",this.statusItemSelector=".module-status-menu",this.statusResetBtnSelector=".module-status-reset",this.importModalBtnSelector="#page-header-desc-configuration-add_module",this.dropZoneModalSelector="#module-modal-import",this.dropZoneModalFooterSelector="#module-modal-import .modal-footer",this.dropZoneImportZoneSelector="#importDropzone",this.moduleImportModalCloseBtn="#module-modal-import-closing-cross",this.moduleImportStartSelector=".module-import-start",this.moduleImportProcessingSelector=".module-import-processing",this.moduleImportSuccessSelector=".module-import-success",this.moduleImportSuccessConfigureBtnSelector=".module-import-success-configure",this.moduleImportFailureSelector=".module-import-failure",this.moduleImportFailureRetrySelector=".module-import-failure-retry",this.moduleImportFailureDetailsBtnSelector=".module-import-failure-details-action",this.moduleImportSelectFileManualSelector=".module-import-start-select-manual",this.moduleImportFailureMsgDetailsSelector=".module-import-failure-details",this.moduleImportConfirmSelector=".module-import-confirm",this.initSortingDropdown(),this.initBOEventRegistering(),this.initCurrentDisplay(),this.initSortingDisplaySwitch(),this.initBulkDropdown(),this.initSearchBlock(),this.initCategorySelect(),this.initCategoriesGrid(),this.initActionButtons(),this.initAddModuleAction(),this.initDropzone(),this.initPageChangeProtection(),this.initPlaceholderMechanism(),this.initFilterStatusDropdown(),this.fetchModulesList(),this.getNotificationsCount(),this.initializeSeeMore()}initFilterStatusDropdown(){const e=this,t=Z("body");t.on("click",e.statusItemSelector,(function(){e.currentRefStatus=parseInt(Z(this).data("status-ref"),10),Z(e.statusSelectorLabelSelector).text(Z(this).text()),Z(e.statusResetBtnSelector).show(),e.updateModuleVisibility()})),t.on("click",e.statusResetBtnSelector,(function(){Z(e.statusSelectorLabelSelector).text(Z(this).text()),Z(this).hide(),e.currentRefStatus=null,e.updateModuleVisibility()}))}initBulkDropdown(){const e=this,t=Z("body");t.on("click",e.getBulkCheckboxesSelector(),(()=>{const t=Z(e.bulkActionDropDownSelector);Z(e.getBulkCheckboxesCheckedSelector()).length>0?t.closest(".module-top-menu-item").removeClass("disabled"):t.closest(".module-top-menu-item").addClass("disabled")})),t.on("click",e.bulkItemSelector,(function(){if(0===Z(e.getBulkCheckboxesCheckedSelector()).length)return void Z.growl.warning({message:window.translate_javascripts["Bulk Action - One module minimum"]});e.lastBulkAction=Z(this).data("ref");const t=e.buildBulkActionModuleList(),o=Z(this).find(":checked").text().toLowerCase();Z(e.bulkConfirmModalListSelector).html(t),Z(e.bulkConfirmModalActionNameSelector).text(o),"bulk-uninstall"===e.lastBulkAction?Z(e.bulkActionCheckboxSelector).show():Z(e.bulkActionCheckboxSelector).hide(),Z(e.bulkConfirmModalSelector).modal("show")})),t.on("click",this.bulkConfirmModalAckBtnSelector,(t=>{t.preventDefault(),t.stopPropagation(),Z(e.bulkConfirmModalSelector).modal("hide"),e.doBulkAction(e.lastBulkAction)}))}initBOEventRegistering(){this.eventEmitter.on("Module Enabled",(e=>this.onModuleDisabled(e))),this.eventEmitter.on("Module Disabled",(e=>this.onModuleDisabled(e))),this.eventEmitter.on("Module Uninstalled",(e=>this.installHandler(e))),this.eventEmitter.on("Module Delete",(e=>this.onModuleDelete(e))),this.eventEmitter.on("Module Installed",(e=>this.installHandler(e)))}installHandler(e){this.updateModuleStatus(e),this.updateModuleVisibility()}updateModuleStatus(e){this.modulesList=this.modulesList.map((t=>{const o=Z(e);if(o.data("tech-name")===t.techName&&void 0!==o.data("version")){return{domObject:o,id:o.data("id"),name:o.data("name").toLowerCase(),scoring:parseFloat(o.data("scoring")),logo:o.data("logo"),author:o.data("author").toLowerCase(),version:o.data("version"),description:o.data("description").toLowerCase(),techName:o.data("tech-name").toLowerCase(),childCategories:o.data("child-categories"),categories:String(o.data("categories")).toLowerCase(),type:o.data("type"),price:parseFloat(o.data("price")),active:parseInt(o.data("active"),10),installed:1===o.data("installed"),access:o.data("last-access"),display:o.hasClass("module-item-list")?this.DISPLAY_LIST:this.DISPLAY_GRID,container:t.container}}return t}))}onModuleDisabled(e){const t=this;t.updateModuleStatus(e),t.getModuleItemSelector(),Z(".modules-list").each((()=>{t.updateModuleVisibility()}))}onModuleDelete(e){this.modulesList=this.modulesList.filter((t=>t.techName!==Z(e).data("tech-name"))),this.installHandler(e)}initPlaceholderMechanism(){const e=this;Z(e.placeholderGlobalSelector).length&&e.ajaxLoadPage(),Z("body").on("click",e.placeholderFailureRetryBtnSelector,(()=>{Z(e.placeholderFailureGlobalSelector).fadeOut(),Z(e.placeholderGlobalSelector).fadeIn(),e.ajaxLoadPage()}))}ajaxLoadPage(){const e=this;Z.ajax({method:"GET",url:window.moduleURLs.catalogRefresh}).done((t=>{if(!0===t.status){void 0===t.domElements&&(t.domElements=null),void 0===t.msg&&(t.msg=null);const o=document.styleSheets[0],i="{display: none}",n=".modules-list",r=".module-sorting-menu",s=`${n},${r}`;o.insertRule?o.insertRule(s+i,o.cssRules.length):o.addRule&&o.addRule(s,i,-1),Z(e.placeholderGlobalSelector).fadeOut(800,(()=>{Z.each(t.domElements,((e,t)=>{Z(t.selector).append(t.content)})),Z(n).fadeIn(800).css("display","flex"),Z(r).fadeIn(800),Z('[data-toggle="popover"]').popover(),e.initCurrentDisplay(),e.fetchModulesList()}))}else Z(e.placeholderGlobalSelector).fadeOut(800,(()=>{Z(e.placeholderFailureMsgSelector).text(t.msg),Z(e.placeholderFailureGlobalSelector).fadeIn(800)}))})).fail((t=>{Z(e.placeholderGlobalSelector).fadeOut(800,(()=>{Z(e.placeholderFailureMsgSelector).text(t.statusText),Z(e.placeholderFailureGlobalSelector).fadeIn(800)}))}))}fetchModulesList(){const e=this;let t,o;e.modulesList=[],Z(".modules-list").each((function(){t=Z(this),t.find(".module-item").each((function(){o=Z(this),e.modulesList.push({domObject:o,id:o.data("id"),name:o.data("name").toLowerCase(),scoring:parseFloat(o.data("scoring")),logo:o.data("logo"),author:o.data("author").toLowerCase(),version:o.data("version"),description:o.data("description").toLowerCase(),techName:o.data("tech-name").toLowerCase(),childCategories:o.data("child-categories"),categories:String(o.data("categories")).toLowerCase(),type:o.data("type"),price:parseFloat(o.data("price")),active:parseInt(o.data("active"),10),installed:1===o.data("installed"),access:o.data("last-access"),display:o.hasClass("module-item-list")?e.DISPLAY_LIST:e.DISPLAY_GRID,container:t}),e.isModulesPage()&&o.remove()}))})),e.updateModuleVisibility(),Z("body").trigger("moduleCatalogLoaded")}updateModuleSorting(){const e=this;if(!e.currentSorting)return;let t="asc",o=e.currentSorting;const i=o.split("-");i.length>1&&(o=i[0],"desc"===i[1]&&(t="desc"));e.modulesList.sort(((e,t)=>{let i=e[o],n=t[o];return"access"===o&&(i=new Date(i).getTime(),n=new Date(n).getTime(),i=Number.isNaN(i)?0:i,n=Number.isNaN(n)?0:n,i===n)?t.name.localeCompare(e.name):i<n?-1:i>n?1:0})),"desc"===t&&e.modulesList.reverse()}updateModuleContainerDisplay(){const e=this;Z(".module-short-list").each((function(){const t=Z(this),o=t.find(".module-item").length;e.currentRefCategory&&e.currentRefCategory!==String(t.find(".modules-list").data("name"))||null!==e.currentRefStatus&&0===o||0===o&&String(t.find(".modules-list").data("name"))===e.CATEGORY_RECENTLY_USED||e.currentTagsList.length>0&&0===o?t.hide():(t.show(),t.find(`${e.seeMoreSelector}, ${e.seeLessSelector}`).toggle(o>=e.DEFAULT_MAX_PER_CATEGORIES))}))}updateModuleVisibility(){const e=this;let t,o,i,n,r,s;e.updateModuleSorting(),e.isModulesPage()&&!e.isReadMoreModalOpened()&&(Z(e.recentlyUsedSelector).find(".module-item").remove(),Z(".modules-list").find(".module-item").remove());const l=new URL(document.location).searchParams.get("find");l&&!0!==e.findModuleUsed?(e.currentTagsList.push(l),e.findModuleUsed=!0):l&&e.currentTagsList.pop(l);const a=e.modulesList.length,c={},d=(e,t)=>{r=t.toLowerCase(),n|=-1!==o.name.indexOf(r)||-1!==o.description.indexOf(r)||-1!==o.author.indexOf(r)||-1!==o.techName.indexOf(r)};for(let r=0;r<a;r+=1)o=e.modulesList[r],o.display===e.currentDisplay&&(t=!0,i=e.currentRefCategory===e.CATEGORY_RECENTLY_USED?e.CATEGORY_RECENTLY_USED:o.categories,null!==e.currentRefCategory&&(t&=i===e.currentRefCategory),null!==e.currentRefStatus&&(t&=o.active===e.currentRefStatus&&!0===o.installed||!1===o.installed&&2===e.currentRefStatus||!0===o.installed&&3===e.currentRefStatus),e.currentTagsList.length&&(n=!1,Z.each(e.currentTagsList,d),t&=n),e.currentDisplay!==e.DISPLAY_LIST||e.currentTagsList.length||(void 0===e.currentCategoryDisplay[i]&&(e.currentCategoryDisplay[i]=!1),c[i]||(c[i]=0),s=i===e.CATEGORY_RECENTLY_USED?e.DEFAULT_MAX_RECENTLY_USED:e.DEFAULT_MAX_PER_CATEGORIES,c[i]>=s&&t&&(t&=e.currentCategoryDisplay[i])),t&&(c[i]+=1,e.currentRefCategory===e.CATEGORY_RECENTLY_USED?Z(e.recentlyUsedSelector).append(o.domObject):o.container.append(o.domObject)));e.updateModuleContainerDisplay(),e.updateTotalResults()}initPageChangeProtection(){const e=this;Z(window).on("beforeunload",(()=>{if(!0===e.isUploadStarted)return"It seems some critical operation are running, are you sure you want to change page? It might cause some unexepcted behaviors."}))}buildBulkActionModuleList(){const e=this.getBulkCheckboxesCheckedSelector(),t=this.getModuleItemSelector();let o,i=0,n="";return Z(e).each((function(){return 10===i?(n+="- ...",!1):(o=Z(this).closest(t),n+=`- ${o.data("name")}<br/>`,i+=1,!0)})),n}initAddModuleAction(){const e=Z(this.importModalBtnSelector);e.attr("data-toggle","modal"),e.attr("data-target",this.dropZoneModalSelector)}initDropzone(){const e=this,t=Z("body"),o=Z(".dropzone");t.on("click",this.moduleImportFailureRetrySelector,(()=>{Z(`${e.moduleImportSuccessSelector},${e.moduleImportFailureSelector},${e.moduleImportProcessingSelector}`).fadeOut((()=>{setTimeout((()=>{Z(e.moduleImportStartSelector).fadeIn((()=>{Z(e.moduleImportFailureMsgDetailsSelector).hide(),Z(e.moduleImportSuccessConfigureBtnSelector).hide(),o.removeAttr("style")}))}),550)}))})),t.on("hidden.bs.modal",this.dropZoneModalSelector,(()=>{Z(`${e.moduleImportSuccessSelector}, ${e.moduleImportFailureSelector}`).hide(),Z(e.moduleImportStartSelector).show(),o.removeAttr("style"),Z(e.moduleImportFailureMsgDetailsSelector).hide(),Z(e.moduleImportSuccessConfigureBtnSelector).hide(),Z(e.dropZoneModalFooterSelector).html(""),Z(e.moduleImportConfirmSelector).hide()})),t.on("click",`.dropzone:not(${this.moduleImportSelectFileManualSelector}, ${this.moduleImportSuccessConfigureBtnSelector})`,((e,t)=>{void 0===t&&(e.stopPropagation(),e.preventDefault())})),t.on("click",this.moduleImportSelectFileManualSelector,(e=>{e.stopPropagation(),e.preventDefault(),Z(".dz-hidden-input").trigger("click",["manual_select"])})),t.on("click",this.moduleImportModalCloseBtn,(()=>{!0!==e.isUploadStarted&&Z(e.dropZoneModalSelector).modal("hide")})),t.on("click",this.moduleImportSuccessConfigureBtnSelector,(function(e){e.stopPropagation(),e.preventDefault(),window.location=Z(this).attr("href")})),t.on("click",this.moduleImportFailureDetailsBtnSelector,(()=>{Z(e.moduleImportFailureMsgDetailsSelector).slideDown()}));const i={url:window.moduleURLs.moduleImport,acceptedFiles:".zip, .tar",paramName:"file_uploaded",uploadMultiple:!1,addRemoveLinks:!0,dictDefaultMessage:"",hiddenInputContainer:e.dropZoneImportZoneSelector,timeout:0,addedfile:()=>{Z(`${e.moduleImportSuccessSelector}, ${e.moduleImportFailureSelector}`).hide(),e.animateStartUpload()},processing:()=>{},error:(t,o)=>{e.displayOnUploadError(o)},complete:t=>{if("error"!==t.status){const o=Z.parseJSON(t.xhr.response);void 0===o.is_configurable&&(o.is_configurable=null),void 0===o.module_name&&(o.module_name=null),e.displayOnUploadDone(o);const i=Z(`<div data-tech-name="${o.module_name}"></div>`);this.eventEmitter.emit(o.upgraded?"Module Upgraded":"Module Installed",i)}e.isUploadStarted=!1}};o.dropzone(Z.extend(i))}animateStartUpload(){const e=this,t=Z(".dropzone");e.isUploadStarted=!0,Z(e.moduleImportStartSelector).hide(0),t.css("border","none"),Z(e.moduleImportProcessingSelector).fadeIn()}animateEndUpload(e){Z(this.moduleImportProcessingSelector).finish().fadeOut(e)}displayOnUploadDone(e){const t=this;t.animateEndUpload((()=>{if(!0===e.status){if(!0===e.is_configurable){const o=window.moduleURLs.configurationPage.replace(/:number:/,e.module_name);Z(t.moduleImportSuccessConfigureBtnSelector).attr("href",o),Z(t.moduleImportSuccessConfigureBtnSelector).show()}Z(t.moduleImportSuccessSelector).fadeIn()}else Z(t.moduleImportFailureMsgDetailsSelector).html(e.msg),Z(t.moduleImportFailureSelector).fadeIn()}))}displayOnUploadError(e){const t=this;t.animateEndUpload((()=>{Z(t.moduleImportFailureMsgDetailsSelector).html(e),Z(t.moduleImportFailureSelector).fadeIn()}))}getBulkCheckboxesSelector(){return this.currentDisplay===this.DISPLAY_GRID?this.bulkActionCheckboxGridSelector:this.bulkActionCheckboxListSelector}getBulkCheckboxesCheckedSelector(){return this.currentDisplay===this.DISPLAY_GRID?this.checkedBulkActionGridSelector:this.checkedBulkActionListSelector}getModuleItemSelector(){return this.currentDisplay===this.DISPLAY_GRID?this.moduleItemGridSelector:this.moduleItemListSelector}getNotificationsCount(){Z.getJSON(window.moduleURLs.notificationsCount,this.updateNotificationsCount).fail((()=>{console.error("Could not retrieve module notifications count.")}))}updateNotificationsCount(e){const t={to_configure:Z("#subtab-AdminModulesNotifications"),to_update:Z("#subtab-AdminModulesUpdates")};Object.keys(t).forEach((o=>{0!==t[o].length&&t[o].find(".notification-counter").text(e[o])}))}initCategoriesGrid(){const e=this;Z("body").on("click",this.categoryGridItemSelector,(function(t){t.stopPropagation(),t.preventDefault();const o=Z(this).data("category-ref");e.currentTagsList.length&&(e.pstaggerInput.resetTags(!1),e.currentTagsList=[]);return Z(`${e.categoryItemSelector}[data-category-ref="${o}"]`).length?(!0===e.isCategoryGridDisplayed&&(Z(e.categoryGridSelector).fadeOut(),e.isCategoryGridDisplayed=!1),Z(`${e.categoryItemSelector}[data-category-ref="${o}"]`).click(),!0):(console.warn(`No category with ref (${o}) seems to exist!`),!1)}))}initCurrentDisplay(){this.currentDisplay=""===this.currentDisplay?this.DISPLAY_LIST:this.DISPLAY_GRID}initSortingDropdown(){const e=this;e.currentSorting=Z(this.moduleSortingDropdownSelector).find(":checked").attr("value"),e.currentSorting||(e.currentSorting="access-desc"),Z("body").on("change",e.moduleSortingDropdownSelector,(function(){e.currentSorting=Z(this).find(":checked").attr("value"),e.updateModuleVisibility()}))}doBulkAction(e){const t=Z("#force_bulk_deletion").prop("checked"),o={"bulk-install":"install","bulk-uninstall":"uninstall","bulk-disable":"disable","bulk-enable":"enable","bulk-disable-mobile":"disableMobile","bulk-enable-mobile":"enableMobile","bulk-reset":"reset","bulk-delete":"delete"};if(void 0===o[e])return Z.growl.error({message:window.translate_javascripts["Bulk Action - Request not found"].replace("[1]",e)}),!1;const i=this.getBulkCheckboxesCheckedSelector(),n=o[e];if(Z(i).length<=0)return console.warn(window.translate_javascripts["Bulk Action - One module minimum"]),!1;const r=[];let s;return Z(i).each((function(){s=Z(this).data("tech-name"),r.push({techName:s,actionMenuObj:Z(this).closest(".module-checkbox-bulk-list").next()})})),this.performModulesAction(r,n,t),!0}performModulesAction(e,t,o){const i=this;if(void 0===i.moduleCardController)return;const n=function(e){const o=[];let n;return Z.each(e,((e,r)=>{n=Z(i.moduleCardController.moduleActionMenuLinkSelector+t,r.actionMenuObj),n.length>0?o.push(n):Z.growl.error({message:window.translate_javascripts["Bulk Action - Request not available for module"].replace("[1]",t).replace("[2]",r.techName)})})),o}(e);function r(){if(n.length<=0)return;!function(e){i.moduleCardController.hasPendingRequest()?n.push(e):i.moduleCardController.requestToController(t,e,o,r)}(n.shift())}n.length&&r()}initActionButtons(){const e=this;Z("body").on("click",e.moduleInstallBtnSelector,(function(e){const t=Z(this),o=Z(t.next());e.preventDefault(),t.hide(),o.show(),Z.ajax({url:t.data("url"),dataType:"json"}).done((()=>{o.fadeOut()}))})),Z("body").on("click",e.upgradeAllSource,(t=>{t.preventDefault();const o=window.isShopMaintenance,i=document.createElement("a");i.classList.add("btn","btn-primary","btn-lg"),i.setAttribute("href",window.moduleURLs.maintenancePage),i.innerHTML=window.moduleTranslations.moduleModalUpdateMaintenance;return new F({id:"confirm-module-update-modal",confirmTitle:window.moduleTranslations.singleModuleModalUpdateTitle,closeButtonLabel:window.moduleTranslations.moduleModalUpdateCancel,confirmButtonLabel:o?window.moduleTranslations.moduleModalUpdateUpgrade:window.moduleTranslations.upgradeAnywayButtonText,confirmButtonClass:o?"btn-primary":"btn-secondary",confirmMessage:o?"":window.moduleTranslations.moduleModalUpdateConfirmMessage,closable:!0,customButtons:o?[]:[i]},(()=>{if(Z(e.upgradeAllTargets).length<=0)return console.warn(window.translate_javascripts["Upgrade All Action - One module minimum"]),!1;const t=[];let o;return Z(e.upgradeAllTargets).each((function(){const e=Z(this).closest(".module-item-list");o=e.data("tech-name"),t.push({techName:o,actionMenuObj:Z(".module-actions",e)})})),this.performModulesAction(t,"upgrade"),!0})).show(),!0}))}initCategorySelect(){const e=this,t=Z("body");t.on("click",e.categoryItemSelector,(function(){e.currentRefCategory=Z(this).data("category-ref"),e.currentRefCategory=e.currentRefCategory?String(e.currentRefCategory).toLowerCase():null,Z(e.categorySelectorLabelSelector).text(Z(this).data("category-display-name")),Z(e.categoryResetBtnSelector).show(),e.updateModuleVisibility()})),t.on("click",e.categoryResetBtnSelector,(function(){const t=Z(e.categorySelector).attr("aria-labelledby"),o=t.charAt(0).toUpperCase()+t.slice(1);Z(e.categorySelectorLabelSelector).text(o),Z(this).hide(),e.currentRefCategory=null,e.updateModuleVisibility()}))}initSearchBlock(){const e=this;e.pstaggerInput=Z("#module-search-bar").pstagger({onTagsChanged:t=>{e.currentTagsList=t,e.updateModuleVisibility()},onResetTags:()=>{e.currentTagsList=[],e.updateModuleVisibility()},inputPlaceholder:window.translate_javascripts["Search - placeholder"],closingCross:!0,context:e})}initSortingDisplaySwitch(){const e=this;Z("body").on("click",".module-sort-switch",(function(){const t=Z(this).data("switch"),o=Z(this).hasClass("active-display");void 0!==t&&!1===o&&(e.switchSortingDisplayTo(t),e.currentDisplay=t)}))}switchSortingDisplayTo(e){e===this.DISPLAY_GRID||e===this.DISPLAY_LIST?(Z(".module-sort-switch").removeClass("module-sort-active"),Z(`#module-sort-${e}`).addClass("module-sort-active"),this.currentDisplay=e,this.updateModuleVisibility()):console.error(`Can't switch to undefined display property "${e}"`)}initializeSeeMore(){const e=this;Z(`${e.moduleShortList} ${e.seeMoreSelector}`).on("click",(function(){e.currentCategoryDisplay[Z(this).data("category")]=!0,Z(this).addClass("d-none"),Z(this).closest(e.moduleShortList).find(e.seeLessSelector).removeClass("d-none"),e.updateModuleVisibility()})),Z(`${e.moduleShortList} ${e.seeLessSelector}`).on("click",(function(){e.currentCategoryDisplay[Z(this).data("category")]=!1,Z(this).addClass("d-none"),Z(this).closest(e.moduleShortList).find(e.seeMoreSelector).removeClass("d-none"),e.updateModuleVisibility()}))}updateTotalResults(){const e=this,t=(e,t)=>{const o=e.text().split(" ");o[0]=t,e.text(o.join(" "))},o=Z(".module-short-list");if(o.length>0)o.each((function(){const e=Z(this);t(e.find(".module-search-result-wording"),e.next(".modules-list").find(".module-item").length)}));else{const o=Z(".modules-list").find(".module-item").length;t(Z(".module-search-result-wording"),o);const i=e.currentDisplay===e.DISPLAY_LIST?this.addonItemListSelector:this.addonItemGridSelector;Z(i).toggle(o!==this.modulesList.length/2)}}isModulesPage(){return 0===Z(this.upgradeContainer).length&&0===Z(this.notificationContainer).length}isReadMoreModalOpened(){return Z(".modal-read-more").is(":visible")}},{$:X}=window;class J{constructor(){J.handleImport()}static handleImport(){const e=X("#module-import");function t(){setTimeout((()=>{e.removeClass("onclick"),e.addClass("validate",450,o)}),2250)}function o(){setTimeout((()=>{e.removeClass("validate")}),1250)}e.click((()=>{e.addClass("onclick",250,t)}))}}const Q=J,{$:K}=window;K((()=>{const e=new H;new Q,new W(e)}))})(),window.module=i})();