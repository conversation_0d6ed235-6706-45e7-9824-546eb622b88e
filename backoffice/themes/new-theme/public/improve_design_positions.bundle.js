(()=>{"use strict";var o={r:o=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})}},e={};o.r(e);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:t}=window;const s=class{constructor(){var o;this.$panelSelection=t("#modules-position-selection-panel"),this.$panelSelectionSingleSelection=t("#modules-position-single-selection"),this.$panelSelectionMultipleSelection=t("#modules-position-multiple-selection");const e=t("#content-message-box + .alert");this.$panelSelectionOriginalY=null==(o=this.$panelSelection.offset())?void 0:o.top,e.length>0&&(this.$panelSelectionOriginalY+=e.outerHeight()),this.$showModules=t("#show-modules"),this.$modulesList=t(".modules-position-checkbox"),this.$hookPosition=t("#hook-position"),this.$hookSearch=t("#hook-search"),this.$modulePositionsForm=t("#module-positions-form"),this.$moduleUnhookButton=t("#unhook-button-position-bottom"),this.$moduleButtonsUpdate=t(".module-buttons-update .btn"),this.$hooksList=[],this.$transplantModuleButton=t(".transplant-module-button"),this.handleList(),this.handleSortable(),this.$modulesList.trigger("change"),this.$modulesList.trigger("scroll"),t('input[name="general[enable_tos]"]').on("change",(()=>{this.handleList(),this.handleSortable()}))}handleList(){const o=this;t(window).on("scroll",(()=>{const e=t(window).scrollTop();o.$panelSelection.css("top",e<20?0:e-o.$panelSelectionOriginalY)})),o.$modulesList.on("change",(()=>{const e=o.$modulesList.filter(":checked").length;0===e?(o.$moduleUnhookButton.hide(),o.$panelSelection.hide(),o.$panelSelectionSingleSelection.hide(),o.$panelSelectionMultipleSelection.hide()):1===e?(o.$moduleUnhookButton.show(),o.$panelSelection.show(),o.$panelSelectionSingleSelection.show(),o.$panelSelectionMultipleSelection.hide()):(o.$moduleUnhookButton.show(),o.$panelSelection.show(),o.$panelSelectionSingleSelection.hide(),o.$panelSelectionMultipleSelection.show(),t("#modules-position-selection-count").html(e))})),o.$panelSelection.find("button").click((()=>{t('button[name="unhookform"]').trigger("click")})),o.$hooksList=[],t("section.hook-panel .hook-name").each((function(){const e=t(this);o.$hooksList.push({title:e.html(),element:e,container:e.parents(".hook-panel")})})),o.$showModules.select2(),o.$showModules.on("change",(()=>{o.modulesPositionFilterHooks()})),o.$hookPosition.on("change",(()=>{o.modulesPositionFilterHooks()})),o.$hookSearch.on("input",(()=>{o.modulesPositionFilterHooks()})),o.modulesPositionFilterHooks(),o.$hookSearch.on("keypress",(o=>13!==(o.keyCode||o.which))),t(".hook-checker").on("click",(function(){t(`.hook${t(this).data("hook-id")}`).prop("checked",t(this).prop("checked"))})),o.$modulesList.on("click",(function(){t(`#Ghook${t(this).data("hook-id")}`).prop("checked",0===t(`.hook${t(this).data("hook-id")}:not(:checked)`).length)})),o.$moduleButtonsUpdate.on("click",(function(){const e=t(this),s=e.closest(".module-item");let i;return i=e.data("way")?s.next(".module-item"):s.prev(".module-item"),0===i.length||(e.data("way")?s.insertAfter(i):s.insertBefore(i),o.updatePositions({hookId:e.data("hook-id"),moduleId:e.data("module-id"),way:e.data("way"),positions:[]},e.closest("ul"))),!1}))}handleSortable(){const o=this;t(".sortable").sortable({forcePlaceholderSize:!0,start(o,e){t(this).data("previous-index",e.item.index())},update(e,s){const[i,n]=s.item.attr("id").split("_"),l={hookId:i,moduleId:n,way:t(this).data("previous-index")<s.item.index()?1:0,positions:[]};o.updatePositions(l,t(e.target))}})}updatePositions(o,e){t.each(e.children(),((e,s)=>{o.positions.push(t(s).attr("id"))})),t.ajax({type:"POST",headers:{"cache-control":"no-cache"},url:this.$modulePositionsForm.data("update-url"),data:o,success:()=>{let o=0;t.each(e.children(),((e,s)=>{o+=1,t(s).find(".index-position").html(o)})),window.showSuccessMessage(window.update_success_msg)}})}modulesPositionFilterHooks(){const o=this,e=o.$hookSearch.val(),s=o.$showModules.val(),i=new RegExp(`(${e})`,"gi"),n=new URL(this.$transplantModuleButton.prop("href"));n.searchParams.set("show_modules",s),this.$transplantModuleButton.attr("href",n.toString());for(let t=0;t<o.$hooksList.length;t+=1)o.$hooksList[t].container.toggle(""===e&&"all"===s),o.$hooksList[t].element.html(o.$hooksList[t].title),o.$hooksList[t].container.find(".module-item").removeClass("highlight");if(""!==e||"all"!==s){let n,l,h=t(),a=t();for(let t=0;t<o.$hooksList.length;t+=1)"all"!==s&&(n=o.$hooksList[t].container.find(`.module-position-${s}`),n.length>0&&(h=h.add(o.$hooksList[t].container),n.addClass("highlight"))),""!==e&&(l=o.$hooksList[t].title.toLowerCase().search(e.toLowerCase()),-1!==l&&(a=a.add(o.$hooksList[t].container),o.$hooksList[t].element.html(o.$hooksList[t].title.replace(i,'<span class="highlight">$1</span>'))));"all"===s&&""!==e?a.show():""===e&&"all"!==s?h.show():a.filter(h).show()}if(!o.$hookPosition.prop("checked"))for(let e=0;e<o.$hooksList.length;e+=1)o.$hooksList[e].container.is(".hook-position")&&o.$hooksList[e].container.hide()}},{$:i}=window;const n=class{constructor(){const o=this;this.$hookStatus=i(".hook-switch-action"),this.$modulePositionsForm=i("#module-positions-form"),this.$hookStatus.on("change",(function(e){e.stopImmediatePropagation(),o.toogleHookStatus(i(this))}))}toogleHookStatus(o){i.ajax({type:"POST",headers:{"cache-control":"no-cache"},url:this.$modulePositionsForm.data("togglestatus-url"),data:{hookId:o.data("hook-id")},success(e){if(e.status){window.showSuccessMessage(e.message);o.closest(".hook-panel").find(".module-list, .module-list-disabled").fadeTo(500,e.hook_status?1:.5)}else window.showErrorMessage(e.message)}})}},{$:l}=window;l((()=>{new s,new n})),window.improve_design_positions=e})();