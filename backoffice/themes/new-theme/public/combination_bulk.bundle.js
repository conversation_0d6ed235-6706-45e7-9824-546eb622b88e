(()=>{"use strict";var e={9567:e=>{e.exports=window.jQuery}},t={};function o(i){var n=t[i];if(void 0!==n)return n.exports;var a=t[i]={exports:{}};return e[i](a,a.exports,o),a.exports}o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};(()=>{o.r(i);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const e="#combination_list",t="#product_details_attachments",n="combination-is-selected",a="bulk-select-all",r="bulk-select-all-in-page",c="shop-preview-row",l={productForm:"form[name=product]",productLocalizedNameInput:'input[name^="product[header][name]"]',productNameLocaleSelector:".header-name .js-locale-btn",productLocalizedLinkRewriteInput:'input[name^="product[seo][link_rewrite]"]',productTypePreview:".product-type-preview",summaryTotalQuantityContainer:'.product-field-preview[data-role="quantity"]',summaryTotalQuantity:'.product-field-preview[data-role="quantity"] .product-total-quantity',summaryTotalQuantityLabel:'.product-field-preview[data-role="quantity"] .product-total-quantity-label',onlineSwitch:"#product_header_active input",productType:{headerSelector:"#product_header_type",headerPreviewButton:".product-type-preview",switchModalId:"switch-product-type-modal",switchModalSelector:"#switch-product-type-modal .header-product-type-selector",switchModalContent:"#product-type-selector-modal-content",switchModalButton:"#switch-product-type-modal .btn-confirm-submit",productTypeSelector:{choicesContainer:".product-type-choices",typeChoices:".product-type-choice",defaultChoiceClass:"btn-outline-secondary",selectedChoiceClass:"btn-primary",typeDescription:".product-type-description-content"}},create:{newProductButton:".new-product-button",createModalSelector:"#create_product_type",modalId:"modal-create-product",form:"form.product-form",createFieldId:"#create_product",modalSizeContainer:".create-product-form"},shops:{modalButtons:"a.product-shops-action",modalId:"modal-product-shops",form:'form[name="product_shops"]',modalSizeContainer:".product-shops-form",cancelButton:"#product_shops_buttons_cancel",editProductClass:"multi-shop-edit-product",selectorItem:".shop-selector-item",shopItemClass:"shop-selector-shop-item",groupShopItemClass:"shop-selector-group-item",shopListCell:".column-associated_shops .product-shop-list",contextWarning:".multi-shop-context-warning",shopPreviews:{toggleButtons:".product-shop-details-toggle",loadingRowClass:"loading-shop-row",expandedShopRowClass:"expanded-shop-row",shopPreviewRowClass:c,productPreviewsSelector:e=>`.${c}[data-product-id="${e}"]`}},invalidField:".is-invalid",productFormSubmitButton:".product-form-save-button",navigationBar:"#form-nav",dropzoneImagesContainer:".product-image-dropzone",manageShopImagesButtonContainer:".manage-shop-images-button-container",manageShopImagesButton:".manage-shop-images-button",featureValues:{collectionContainer:".feature-values-collection",collectionRowsContainer:".feature-values-collection > .col-sm",collectionRow:"div.product-feature",featureSelect:"select.feature-selector",featureValueSelect:"select.feature-value-selector",customValueInput:".custom-values input",customFeatureIdInput:"input.custom-value-id",deleteFeatureValue:"button.delete-feature-value",addFeatureValue:".feature-value-add-button"},customizations:{customizationsContainer:".product-customizations-collection",customizationFieldsList:".product-customizations-collection ul",addCustomizationBtn:".add-customization-btn",removeCustomizationBtn:".remove-customization-btn",customizationFieldRow:".customization-field-row"},stock:{navigationTarget:"#product_stock-tab"},combinations:{navigationTab:"#product_combinations-tab-nav",navigationTarget:"#product_combinations-tab",combinationManager:"#product_combinations_combination_manager",preloader:"#combinations-preloader",emptyState:"#combinations-empty-state",emptyFiltersState:"#combinations-empty-filters-state",combinationsPaginatedList:"#combinations-paginated-list",combinationsFormContainer:"#combinations-list-form-container",combinationsFiltersContainer:"#combinations_filters",filtersSelectorButtons:"#combinations_filters .ps-checkboxes-dropdown button.dropdown-toggle",combinationsGeneratorContainer:"#product_combinations_generator",combinationsTable:`${e}`,combinationsTableBody:`${e} tbody`,combinationIdInputsSelector:".combination-id-input",deleteCombinationSelector:".delete-combination-item",deleteCombinationAllShopsSelector:".delete-combination-all-shops",combinationName:"form .combination-name-row .text-preview-value",paginationContainer:"#combinations-pagination",loadingSpinner:"#productCombinationsLoading",impactOnPriceInputWrapper:".combination-impact-on-price",referenceInputWrapper:".combination-reference",sortableColumns:".ps-sortable-column",combinationItemForm:{isDefaultKey:"combination_item[is_default]",deltaQuantityKey:"combination_item[delta_quantity][delta]",impactOnPriceKey:"combination_item[impact_on_price][value]",referenceKey:"combination_item[reference][value]",tokenKey:"combination_item[_token]"},editionForm:'form[name="combination_form"]',editionFormInputs:'form[name="combination_form"] input, form[name="combination_form"] textarea, form[name="combination_form"] select',editCombinationButtons:".edit-combination-item",tableRow:{isSelectedCombination:`.${n}`,combinationImg:".combination-image",deltaQuantityWrapper:".delta-quantity",deltaQuantityInput:t=>`${e}_combinations_${t}_delta_quantity_delta`,combinationCheckbox:t=>`${e}_combinations_${t}_is_selected`,combinationIdInput:t=>`${e}_combinations_${t}_combination_id`,combinationNameInput:t=>`${e}_combinations_${t}_name`,referenceInput:t=>`${e}_combinations_${t}_reference_value`,impactOnPriceInput:t=>`${e}_combinations_${t}_impact_on_price_value`,finalPriceTeInput:t=>`${e}_combinations_${t}_final_price_te`,quantityInput:t=>`${e}_combinations_${t}_delta_quantity_quantity`,isDefaultInput:t=>`${e}_combinations_${t}_is_default`,editButton:t=>`${e}_combinations_${t}_edit`,deleteButton:t=>`${e}_combinations_${t}_delete`},list:{attributeFilterInputName:"combination-attribute-filter",combinationRow:".combination-list-row",priceImpactTaxExcluded:".combination-impact-on-price-tax-excluded",priceImpactTaxIncluded:".combination-impact-on-price-tax-included",isDefault:".combination-is-default-input",ecoTax:".combination-eco-tax",finalPrice:".combination-final-price",finalPricePreview:".text-preview",modifiedFieldClass:"combination-value-changed",invalidClass:"is-invalid",editionModeClass:"combination-edition-mode",fieldInputs:`.combination-list-row :input:not(.${a}):not(.${n})`,errorAlerts:".combination-list-row .alert-danger",rowActionButtons:".combination-row-actions button, .combination-row-actions .dropdown-toggle",footer:{cancel:"#cancel-combinations-edition",save:"#save-combinations-edition"}},availabilityContainer:".combination-availability",editModal:"#combination-edit-modal",images:{selectorContainer:".combination-images-selector",imageChoice:".combination-image-choice",checkboxContainer:".form-check",checkbox:"input[type=checkbox]"},scrollBar:".attributes-list-overflow",searchInput:"#product-combinations-generate .attributes-search",generateCombinationsButton:".generate-combinations-button",bulkCombinationFormBtn:"#combination-bulk-form-btn",bulkDeleteBtn:".bulk-delete-btn",bulkDeleteBtnAllShopsId:"combination-bulk-delete-btn-all-shops",bulkActionBtn:".bulk-action-btn",bulkActionsDropdownBtn:"#combination-bulk-actions-btn",bulkAllPreviewInput:"#bulk-all-preview",bulkSelectAll:"#bulk-select-all",bulkCheckboxesDropdownButton:"#bulk-all-selection-dropdown-button",commonBulkAllSelector:`.${a}`,bulkSelectAllInPage:`#${r}`,bulkSelectAllInPageId:r,bulkProgressModalId:"bulk-combination-progress-modal",bulkFormModalId:"bulk-combination-form-modal",bulkForm:'form[name="bulk_combination"]',bulkDeltaQuantitySwitchName:"bulk_combination[stock][disabling_switch_delta_quantity]",bulkFixedQuantitySwitchName:"bulk_combination[stock][disabling_switch_fixed_quantity]"},virtualProduct:{fileContentContainer:".virtual-product-file-container .virtual-product-file-content",fileUploadInput:"#product_stock_virtual_product_file_file",filenameInput:"#product_stock_virtual_product_file_name"},dropzone:{configuration:{fileManager:".openfilemanager"},photoswipe:{element:".pswp"},dzTemplate:".dz-template",dzPreview:".dz-preview",sortableContainer:"#product-images-dropzone",sortableItems:"div.dz-preview:not(.disabled)",dropzoneContainer:".dropzone-container",checkbox:".md-checkbox input",shownTooltips:".tooltip.show",savedImageContainer:e=>`.dz-preview[data-id="${e}"]`,savedImage:e=>`.dz-preview[data-id="${e}"] img`,coveredPreview:".dz-preview.is-cover",windowFileManager:".dropzone-window-filemanager"},options:{availableForOrderInput:'input[name="product[options][visibility][available_for_order]"]',showPriceInput:'input[name="product[options][visibility][show_price]"]',showPriceSwitchContainer:".show-price-switch-container",visibilityRadio:'input[name="product[options][visibility][visibility]"]',visibilityDescriptionField:".js-visibility-description"},suppliers:{productSuppliers:"#product_options_product_suppliers",supplierIdsInput:"#product_options_suppliers_supplier_ids",defaultSupplierInput:"#product_options_suppliers_default_supplier_id"},shipping:{deliveryTimeTypeInput:'input[name="product[shipping][delivery_time_note_type]"]',deliveryTimeNotesBlock:"#product_shipping_delivery_time_notes",carrierSelectorContainer:"#product_shipping_carriers",carrierChoiceLabel:".carrier-choice-label",carrierCheckboxesDropdownId:"carrier-checkboxes-dropdown"},seo:{container:"#product_seo_serp",defaultTitle:".serp-default-title:input",watchedTitle:".serp-watched-title:input",defaultDescription:".serp-default-description",watchedDescription:".serp-watched-description",watchedMetaUrl:".serp-watched-url:input",tagFields:"input.js-taggable-field",redirectOption:{typeInput:"#product_seo_redirect_option_type",targetInput:"#product_seo_redirect_option_target",groupSelector:".form-group",labelSelector:"label",helpSelector:"small.form-text"},resetLinkRewriteBtn:".reset-link-rewrite"},jsTabs:"#product-tabs",jsArrow:"#product-tabs .js-arrow",jsNavTabs:"#product-tabs .js-nav-tabs",toggleTab:'#product-tabs [data-toggle="tab"]',formContentTab:"#product-tabs-content > .form-contenttab",leftArrow:".left-arrow",rightArrow:".right-arrow",footer:{container:".product-footer",previewUrlButton:".preview-url-button",deleteProductButton:".delete-product-button",deleteProductModalId:"delete-product-footer-modal",duplicateProductButton:".duplicate-product-button",duplicateProductModalId:"duplicate-product-footer-modal",newProductButton:".new-product-button",goToCatalogButton:".go-to-catalog-button",cancelButton:".cancel-button"},categories:{categoriesContainer:"#product_description_categories",categoriesModalTemplate:"#categories-modal-template",modalContentContainer:"#categories-modal-content",categoriesModalId:"categories-modal",applyCategoriesBtn:".js-apply-categories-btn",cancelCategoriesBtn:".js-cancel-categories-btn",categoryTree:".js-category-tree-list",treeElement:".category-tree-element",treeElementInputs:".category-tree-inputs",treeCheckboxInput:".tree-checkbox-input",checkboxInput:"[type=checkbox]",checkedCheckboxInputs:"[type=checkbox]:checked",checkboxName:e=>`product[description][categories][product_categories][${e}][is_associated]`,inputByValue:e=>`input[value="${e}"]`,defaultCategorySelectInput:"#product_description_categories_default_category_id",materialCheckbox:".md-checkbox",radioInput:"[type=radio]",defaultRadioInput:"[type=radio]:checked",radioName:e=>`product[description][categories][product_categories][${e}][is_default]`,tagsContainer:".pstaggerTagsWrapper",tagRemoveBtn:".pstaggerClosingCross",tagCategoryIdInput:".category-id-input",tagItem:".tag-item",categoryNamePreview:".category-name-preview",namePreviewInput:".category-name-preview-input",categoryNameInput:".category-name-input",searchInput:"#ps-select-product-category",fieldset:".tree-fieldset",loader:".categories-tree-loader",childrenList:".children-list",addCategoriesBtn:".add-categories-btn",categoryFilter:{container:".product_list_category_filter",categoryRadio:".category-label input:radio",filterForm:"#product_filter_form",positionInput:'input[name="product[position]"]',expandedClass:"less",collapsedClass:"more",categoryChildren:".category-children",categoryLabel:".category-label",categoryLabelClass:"category-label",categoryNode:".category-node",expandAll:".category_tree_filter_expand",collapseAll:".category_tree_filter_collapse",resetFilter:".category_tree_filter_reset"}},modules:{previewContainer:".module-render-container.all-modules",previewButton:".modules-list-button",selectorContainer:".module-selection",moduleSelector:".modules-list-select",selectorPreviews:".module-selection .module-render-container",selectorPreview:e=>`.module-selection .module-render-container.${e}`,contentContainer:".module-contents",moduleContents:".module-contents .module-render-container",moduleContent:e=>`.module-contents .module-render-container.${e}`},attachments:{attachmentsContainer:t,searchAttributeInput:`${t}_attached_files`,addAttachmentBtn:".add-attachment"},conditionSwitch:'input[name="product[details][show_condition]"]',conditionChoiceSelect:"#product_details_condition",relatedProducts:{searchInput:"#product_description_related_products"},priceSummary:{container:".price-summary-widget",priceTaxExcluded:".price-tax-excluded-value",priceTaxIncluded:".price-tax-included-value",unitPrice:".unit-price-value",margin:".margin-value",marginRate:".margin-rate-value",wholesalePrice:".wholesale-price-value",taxRuleGroupHelpLabel:".js-tax-rule-help"},specificPrice:{container:"#specific-prices-container",paginationContainer:"#specific-prices-pagination",loadingSpinner:"#specific-prices-loading",listTable:"#specific-prices-list-table",modalTemplate:"#specific-price-modal-template",modalContentId:"specific-price-modal",addSpecificPriceBtn:".js-add-specific-price-btn",form:'form[name="specific_price"]',listContainer:"#specific-price-list-container",listRowTemplate:"#specific-price-tr-template",deletionModalId:"modal-confirm-delete-combination",listFields:{specificPriceId:".specific-price-id",combination:".combination",currency:".currency",country:".country",group:".group",shop:".shop",customer:".customer",price:".price",impact:".impact",period:".period",from:".period .from",to:".period .to",fromQuantity:".from-qty",editBtn:".js-edit-specific-price-btn",deleteBtn:".js-delete-specific-price-btn"},priority:{priorityListWrapper:".specific-price-priority-list",priorityTypeCheckboxesSelector:'input[name="product[pricing][priority_management][use_custom_priority]"]'}},packedProducts:{searchInput:"#product_stock_packed_products"},catalogPriceRule:{listContainer:"#catalog-price-rule-list-container",paginationContainer:"#catalog-price-rules-pagination",loadingSpinner:"#catalog-price-rules-loading",listTable:"#catalog-price-rules-list-table",listRowTemplate:"#catalog-price-rule-tr-template",showCatalogPriceRules:"#product_pricing_show_catalog_price_rules",blockContainer:"#product_pricing_catalog_price_rules",listFields:{catalogPriceRuleId:".catalog-price-rule-id",shop:".shop",currency:".currency",country:".country",group:".group",name:".name",impact:".impact",from:".from",to:".to",fromQuantity:".from-qty",editBtn:".js-edit-catalog-price-rule-btn"}}},{$:s}=window;class d{constructor(){this.$selectorContainer=s(l.combinations.images.selectorContainer),this.init()}init(){s(l.combinations.images.checkboxContainer,this.$selectorContainer).hide(),this.$selectorContainer.on("click",l.combinations.images.imageChoice,(e=>{if(this.$selectorContainer.hasClass("disabled"))return;const t=s(e.currentTarget),o=s(l.combinations.images.checkbox,t),i=o.prop("checked");t.toggleClass("selected",!i),o.prop("checked",!i)}))}}var u=o(9567);const p=l.combinations,m={refreshPage:"refreshPage",refreshCombinationList:"refreshCombinationList",listEditionMode:"listEditionMode",updateAttributeFilters:"updateAttributeFilters",combinationGeneratorReady:"combinationGeneratorReady",openCombinationsGenerator:"openCombinationsGenerator",clearFilters:"clearFilters",selectCombination:"selectCombination",listRendered:"combinationsListRendered",errorListRendered:"combinationsErrorListRendered",buildCombinationRow:"buildCombinationRow",bulkUpdateFinished:"combinationsBulkUpdateFinished",bulkDeleteFinished:"combinationsBulkDeleteFinished",combinationDeleted:"combinationDeleted",combinationSwitchDeltaQuantity:"combinationSwitchDeltaQuantity",combinationSwitchFixedQuantity:"combinationSwitchFixedQuantity"};class b{constructor(){this.eventEmitter=window.prestashop.component.EventEmitter,this.init()}init(){function e(e,t){const o=u(`[name="${e}"][value="1"]`),i=u(`[name="${e}"][value="0"]`);o.is(":checked")!==t&&o.prop("checked",t),i.is(":checked")===t&&i.prop("checked",!t)}this.eventEmitter.on(m.combinationSwitchDeltaQuantity,(t=>{t.disable||e(p.bulkFixedQuantitySwitchName,!1)})),this.eventEmitter.on(m.combinationSwitchFixedQuantity,(t=>{t.disable||e(p.bulkDeltaQuantitySwitchName,!1)}))}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */const g="#bulk_combination_price_price_tax_excluded",h='input[name="bulk_combination[price][disabling_switch_price_tax_excluded]"]',_="#bulk_combination_price_price_tax_included",f='input[name="bulk_combination[price][disabling_switch_price_tax_included]"]',y="#bulk_combination_price",w='input[name="bulk_combination[stock][low_stock_threshold][low_stock_alert]"]',v="#bulk_combination_stock_low_stock_threshold_threshold_value";
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
function k(e){return void 0===e}var C=Object.defineProperty,I=Object.getOwnPropertySymbols,S=Object.prototype.hasOwnProperty,x=Object.prototype.propertyIsEnumerable,$=(e,t,o)=>t in e?C(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:P}=window;class T{constructor(e){this.params=((e,t)=>{for(var o in t||(t={}))S.call(t,o)&&$(e,o,t[o]);if(I)for(var o of I(t))x.call(t,o)&&$(e,o,t[o]);return e})({matchingValue:"0",disableOnMatch:!0,targetSelector:null,switchEvent:null,toggleType:"availability"},e),this.init()}init(){document.querySelectorAll(this.params.disablingInputSelector).forEach((e=>{this.updateTargetState(e),P(e).on("change",(()=>{this.updateTargetState(e)}))}))}updateTargetState(e){var t,o,i;const n=this.getInputValue(e);if(k(n))return;const a=null!=(t=e.dataset.matchingValue)?t:this.params.matchingValue,r=null!=(o=e.dataset.targetSelector)?o:this.params.targetSelector,c=null!=(i=e.dataset.switchEvent)?i:this.params.switchEvent;let l,{disableOnMatch:s}=this.params;k(e.dataset)||k(e.dataset.disableOnMatch)||(s="1"===e.dataset.disableOnMatch),null!==a?null!==r?(l=n===a?s:!s,this.toggle(r,l,c)):console.error("No target selector defined for inputElement",e):console.error("No matching value defined for inputElement",e)}getInputValue(e){switch(e.type){case"radio":{let t;return document.querySelectorAll(`[name="${e.name}"]`).forEach((e=>{e.checked&&(t=e.value)})),t}case"checkbox":return e.checked?e.value:void 0;default:return e.value}}toggle(e,t,o){if(o){const{eventEmitter:i}=window.prestashop.instance;if(i){const n={targetSelector:e,disable:t};i.emit(o,n)}else console.error("Trying to use EventEmitter without having initialised the component before.")}const i=document.querySelectorAll(e);0!==i.length?i.forEach((e=>{const o="availability"===this.params.toggleType;o?(e.classList.toggle("disabled",t),e.toggleAttribute("disabled",t)):e.classList.toggle("d-none",t);const i=e.querySelectorAll("input, select, textarea, button, option, fieldset");0!==i.length&&i.forEach((e=>{o&&e.toggleAttribute("disabled",t)}))})):console.error(`Could not find target ${e}`)}}
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:B}=window;B((()=>{var e;window.prestashop.component.initComponents(["TranslatableField","TranslatableInput","EventEmitter","DeltaQuantityInput","DisablingSwitch","ModifyAllShopsCheckbox"]),new d,new b,new T({disablingInputSelector:w,targetSelector:v}),function(e,t){function o(e,t){document.querySelectorAll(e).forEach((e=>{e.addEventListener("change",(()=>{let o=`${t}[value="0"]`,i=`${t}[value="1"]`;"1"===e.value&&(o=`${t}[value="1"]`,i=`${t}[value="0"]`);const n=document.querySelector(o),a=document.querySelector(i);n&&!n.checked&&(n.checked=!0,n.dispatchEvent(new Event("change"))),a&&a.checked&&(a.checked=!1,a.dispatchEvent(new Event("change")))}))}))}o(e,t),o(t,e)}(f,h);const t=document.querySelector(y),o=document.querySelector(g),i=document.querySelector(_),n=1+parseFloat(null!=(e=null==t?void 0:t.dataset.rate)?e:"0");o.addEventListener("keyup",(()=>{let e;e=""===o.value?0:parseFloat(o.value),i.value=(e*n).toString()})),i.addEventListener("keyup",(()=>{let e;e=""===i.value?0:parseFloat(i.value),o.value=(e/n).toString()}))}))})(),window.combination_bulk=i})();