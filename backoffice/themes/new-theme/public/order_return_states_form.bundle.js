(()=>{var e={7187:e=>{"use strict";var t,o="object"==typeof Reflect?Reflect:null,r=o&&"function"==typeof o.apply?o.apply:function(e,t,o){return Function.prototype.apply.call(e,t,o)};t=o&&"function"==typeof o.ownKeys?o.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var n=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise((function(o,r){function n(o){e.removeListener(t,i),r(o)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",n),o([].slice.call(arguments))}v(e,t,i,{once:!0}),"error"!==t&&function(e,t,o){"function"==typeof e.on&&v(e,"error",t,o)}(e,n,{once:!0})}))},i.EventEmitter=i,i.prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var a=10;function l(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function s(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function c(e,t,o,r){var n,i,a,c;if(l(o),void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,o.listener?o.listener:o),i=e._events),a=i[t]),void 0===a)a=i[t]=o,++e._eventsCount;else if("function"==typeof a?a=i[t]=r?[o,a]:[a,o]:r?a.unshift(o):a.push(o),(n=s(e))>0&&a.length>n&&!a.warned){a.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,c=u,console&&console.warn&&console.warn(c)}return e}function u(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,o){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:o},n=u.bind(r);return n.listener=o,r.wrapFn=n,n}function p(e,t,o){var r=e._events;if(void 0===r)return[];var n=r[t];return void 0===n?[]:"function"==typeof n?o?[n.listener||n]:[n]:o?function(e){for(var t=new Array(e.length),o=0;o<t.length;++o)t[o]=e[o].listener||e[o];return t}(n):d(n,n.length)}function f(e){var t=this._events;if(void 0!==t){var o=t[e];if("function"==typeof o)return 1;if(void 0!==o)return o.length}return 0}function d(e,t){for(var o=new Array(t),r=0;r<t;++r)o[r]=e[r];return o}function v(e,t,o,r){if("function"==typeof e.on)r.once?e.once(t,o):e.on(t,o);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function n(i){r.once&&e.removeEventListener(t,n),o(i)}))}}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||n(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),i.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||n(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return s(this)},i.prototype.emit=function(e){for(var t=[],o=1;o<arguments.length;o++)t.push(arguments[o]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var l=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw l.context=a,l}var s=i[e];if(void 0===s)return!1;if("function"==typeof s)r(s,this,t);else{var c=s.length,u=d(s,c);for(o=0;o<c;++o)r(u[o],this,t)}return!0},i.prototype.addListener=function(e,t){return c(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return c(this,e,t,!0)},i.prototype.once=function(e,t){return l(t),this.on(e,h(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return l(t),this.prependListener(e,h(this,e,t)),this},i.prototype.removeListener=function(e,t){var o,r,n,i,a;if(l(t),void 0===(r=this._events))return this;if(void 0===(o=r[e]))return this;if(o===t||o.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,o.listener||t));else if("function"!=typeof o){for(n=-1,i=o.length-1;i>=0;i--)if(o[i]===t||o[i].listener===t){a=o[i].listener,n=i;break}if(n<0)return this;0===n?o.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(o,n),1===o.length&&(r[e]=o[0]),void 0!==r.removeListener&&this.emit("removeListener",e,a||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,o,r;if(void 0===(o=this._events))return this;if(void 0===o.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==o[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete o[e]),this;if(0===arguments.length){var n,i=Object.keys(o);for(r=0;r<i.length;++r)"removeListener"!==(n=i[r])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=o[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},i.prototype.listeners=function(e){return p(this,e,!0)},i.prototype.rawListeners=function(e){return p(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):f.call(e,t)},i.prototype.listenerCount=f,i.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},5933:(e,t,o)=>{(function(t,r){
/*!
 * Bootstrap Colorpicker - Bootstrap Colorpicker is a modular color picker plugin for Bootstrap 4.
 * @package bootstrap-colorpicker
 * @version v3.4.0
 * @license MIT
 * @link https://itsjavi.com/bootstrap-colorpicker/
 * @link https://github.com/itsjavi/bootstrap-colorpicker.git
 */
var n,i;n=window,i=function(e){return function(e){var t={};function o(r){if(t[r])return t[r].exports;var n=t[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,o),n.l=!0,n.exports}return o.m=e,o.c=t,o.d=function(e,t,r){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=7)}([function(t,o){t.exports=e},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),i=o(0),a=(r=i)&&r.__esModule?r:{default:r};function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(l(this,e),this.colorpicker=t,this.options=o,!this.colorpicker.element||!this.colorpicker.element.length)throw new Error("Extension: this.colorpicker.element is not valid");this.colorpicker.element.on("colorpickerCreate.colorpicker-ext",a.default.proxy(this.onCreate,this)),this.colorpicker.element.on("colorpickerDestroy.colorpicker-ext",a.default.proxy(this.onDestroy,this)),this.colorpicker.element.on("colorpickerUpdate.colorpicker-ext",a.default.proxy(this.onUpdate,this)),this.colorpicker.element.on("colorpickerChange.colorpicker-ext",a.default.proxy(this.onChange,this)),this.colorpicker.element.on("colorpickerInvalid.colorpicker-ext",a.default.proxy(this.onInvalid,this)),this.colorpicker.element.on("colorpickerShow.colorpicker-ext",a.default.proxy(this.onShow,this)),this.colorpicker.element.on("colorpickerHide.colorpicker-ext",a.default.proxy(this.onHide,this)),this.colorpicker.element.on("colorpickerEnable.colorpicker-ext",a.default.proxy(this.onEnable,this)),this.colorpicker.element.on("colorpickerDisable.colorpicker-ext",a.default.proxy(this.onDisable,this))}return n(e,[{key:"resolveColor",value:function(e){return!1}},{key:"onCreate",value:function(e){}},{key:"onDestroy",value:function(e){this.colorpicker.element.off(".colorpicker-ext")}},{key:"onUpdate",value:function(e){}},{key:"onChange",value:function(e){}},{key:"onInvalid",value:function(e){}},{key:"onHide",value:function(e){}},{key:"onShow",value:function(e){}},{key:"onDisable",value:function(e){}},{key:"onEnable",value:function(e){}}]),e}();t.default=s,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ColorItem=t.HSVAColor=void 0;var r,n=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),i=o(16),a=(r=i)&&r.__esModule?r:{default:r};function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function(){function e(t,o,r,n){l(this,e),this.h=isNaN(t)?0:t,this.s=isNaN(o)?0:o,this.v=isNaN(r)?0:r,this.a=isNaN(t)?1:n}return n(e,[{key:"toString",value:function(){return this.h+", "+this.s+"%, "+this.v+"%, "+this.a}}]),e}(),c=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];l(this,e),this.replace(t,o,r)}return n(e,[{key:"api",value:function(t){for(var o=arguments.length,r=Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n];if(0===arguments.length)return this._color;var i=this._color[t].apply(this._color,r);return i instanceof a.default?new e(i,this.format):i}},{key:"original",get:function(){return this._original}}],[{key:"HSVAColor",get:function(){return s}}]),n(e,[{key:"replace",value:function(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(o=e.sanitizeFormat(o),this._original={color:t,format:o,valid:!0},this._color=e.parse(t,r),null===this._color)return this._color=(0,a.default)(),void(this._original.valid=!1);this._format=o||(e.isHex(t)?"hex":this._color.model)}},{key:"isValid",value:function(){return!0===this._original.valid}},{key:"setHueRatio",value:function(e){this.hue=360*(1-e)}},{key:"setSaturationRatio",value:function(e){this.saturation=100*e}},{key:"setValueRatio",value:function(e){this.value=100*(1-e)}},{key:"setAlphaRatio",value:function(e){this.alpha=1-e}},{key:"isDesaturated",value:function(){return 0===this.saturation}},{key:"isTransparent",value:function(){return 0===this.alpha}},{key:"hasTransparency",value:function(){return this.hasAlpha()&&this.alpha<1}},{key:"hasAlpha",value:function(){return!isNaN(this.alpha)}},{key:"toObject",value:function(){return new s(this.hue,this.saturation,this.value,this.alpha)}},{key:"toHsva",value:function(){return this.toObject()}},{key:"toHsvaRatio",value:function(){return new s(this.hue/360,this.saturation/100,this.value/100,this.alpha)}},{key:"toString",value:function(){return this.string()}},{key:"string",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!(t=e.sanitizeFormat(t||this.format)))return this._color.round().string();if(void 0===this._color[t])throw new Error("Unsupported color format: '"+t+"'");var o=this._color[t]();return o.round?o.round().string():o}},{key:"equals",value:function(t){return!(!(t=t instanceof e?t:new e(t)).isValid()||!this.isValid())&&this.hue===t.hue&&this.saturation===t.saturation&&this.value===t.value&&this.alpha===t.alpha}},{key:"getClone",value:function(){return new e(this._color,this.format)}},{key:"getCloneHueOnly",value:function(){return new e([this.hue,100,100,1],this.format)}},{key:"getCloneOpaque",value:function(){return new e(this._color.alpha(1),this.format)}},{key:"toRgbString",value:function(){return this.string("rgb")}},{key:"toHexString",value:function(){return this.string("hex")}},{key:"toHslString",value:function(){return this.string("hsl")}},{key:"isDark",value:function(){return this._color.isDark()}},{key:"isLight",value:function(){return this._color.isLight()}},{key:"generate",value:function(t){var o=[];if(Array.isArray(t))o=t;else{if(!e.colorFormulas.hasOwnProperty(t))throw new Error("No color formula found with the name '"+t+"'.");o=e.colorFormulas[t]}var r=[],n=this._color,i=this.format;return o.forEach((function(t){var o=[t?(n.hue()+t)%360:n.hue(),n.saturationv(),n.value(),n.alpha()];r.push(new e(o,i))})),r}},{key:"hue",get:function(){return this._color.hue()},set:function(e){this._color=this._color.hue(e)}},{key:"saturation",get:function(){return this._color.saturationv()},set:function(e){this._color=this._color.saturationv(e)}},{key:"value",get:function(){return this._color.value()},set:function(e){this._color=this._color.value(e)}},{key:"alpha",get:function(){var e=this._color.alpha();return isNaN(e)?1:e},set:function(e){this._color=this._color.alpha(Math.round(100*e)/100)}},{key:"format",get:function(){return this._format?this._format:this._color.model},set:function(t){this._format=e.sanitizeFormat(t)}}],[{key:"parse",value:function(t){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t instanceof a.default)return t;if(t instanceof e)return t._color;var r=null;if(null===(t=t instanceof s?[t.h,t.s,t.v,isNaN(t.a)?1:t.a]:e.sanitizeString(t)))return null;if(Array.isArray(t)&&(r="hsv"),e.isHex(t)&&6!==t.length&&7!==t.length&&o)return null;try{return(0,a.default)(t,r)}catch(e){return null}}},{key:"sanitizeString",value:function(e){return"string"==typeof e||e instanceof String?e.match(/^[0-9a-f]{2,}$/i)?"#"+e:"transparent"===e.toLowerCase()?"#FFFFFF00":e:e}},{key:"isHex",value:function(e){return("string"==typeof e||e instanceof String)&&!!e.match(/^#?[0-9a-f]{2,}$/i)}},{key:"sanitizeFormat",value:function(e){switch(e){case"hex":case"hex3":case"hex4":case"hex6":case"hex8":return"hex";case"rgb":case"rgba":case"keyword":case"name":return"rgb";case"hsl":case"hsla":case"hsv":case"hsva":case"hwb":case"hwba":return"hsl";default:return""}}}]),e}();c.colorFormulas={complementary:[180],triad:[0,120,240],tetrad:[0,90,180,270],splitcomplement:[0,72,216]},t.default=c,t.HSVAColor=s,t.ColorItem=c},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=6,n=16*r+6*(r-1);t.default={customClass:null,color:!1,fallbackColor:!1,format:"auto",horizontal:!1,inline:!1,container:!1,popover:{animation:!0,placement:"bottom",fallbackPlacement:"flip"},debug:!1,input:"input",addon:".colorpicker-input-addon",autoInputFallback:!0,autoHexInputFallback:!0,useHashPrefix:!0,useAlpha:!0,template:'<div class="colorpicker">\n      <div class="colorpicker-saturation"><i class="colorpicker-guide"></i></div>\n      <div class="colorpicker-hue"><i class="colorpicker-guide"></i></div>\n      <div class="colorpicker-alpha">\n        <div class="colorpicker-alpha-color"></div>\n        <i class="colorpicker-guide"></i>\n      </div>\n    </div>',extensions:[{name:"preview",options:{showText:!0}}],sliders:{saturation:{selector:".colorpicker-saturation",maxLeft:n,maxTop:n,callLeft:"setSaturationRatio",callTop:"setValueRatio"},hue:{selector:".colorpicker-hue",maxLeft:0,maxTop:n,callLeft:!1,callTop:"setHueRatio"},alpha:{selector:".colorpicker-alpha",childSelector:".colorpicker-alpha-color",maxLeft:0,maxTop:n,callLeft:!1,callTop:"setAlphaRatio"}},slidersHorz:{saturation:{selector:".colorpicker-saturation",maxLeft:n,maxTop:n,callLeft:"setSaturationRatio",callTop:"setValueRatio"},hue:{selector:".colorpicker-hue",maxLeft:n,maxTop:0,callLeft:"setHueRatio",callTop:!1},alpha:{selector:".colorpicker-alpha",childSelector:".colorpicker-alpha-color",maxLeft:n,maxTop:0,callLeft:"setAlphaRatio",callTop:!1}}},e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),i=l(o(1)),a=l(o(0));function l(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u={colors:null,namesAsValues:!0},h=function(e){function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(this,t);var n=c(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a.default.extend(!0,{},u,o)));return Array.isArray(n.options.colors)||"object"===r(n.options.colors)||(n.options.colors=null),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"colors",get:function(){return this.options.colors}}]),n(t,[{key:"getLength",value:function(){return this.options.colors?Array.isArray(this.options.colors)?this.options.colors.length:"object"===r(this.options.colors)?Object.keys(this.options.colors).length:0:0}},{key:"resolveColor",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return!(this.getLength()<=0)&&(Array.isArray(this.options.colors)?this.options.colors.indexOf(e)>=0?e:this.options.colors.indexOf(e.toUpperCase())>=0?e.toUpperCase():this.options.colors.indexOf(e.toLowerCase())>=0&&e.toLowerCase():"object"===r(this.options.colors)&&(!this.options.namesAsValues||t?this.getValue(e,!1):this.getName(e,this.getName("#"+e))))}},{key:"getName",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("string"!=typeof e||!this.options.colors)return t;for(var o in this.options.colors)if(this.options.colors.hasOwnProperty(o)&&this.options.colors[o].toLowerCase()===e.toLowerCase())return o;return t}},{key:"getValue",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"==typeof e&&this.options.colors&&this.options.colors.hasOwnProperty(e)?this.options.colors[e]:t}}]),t}(i.default);t.default=h,e.exports=t.default},function(e,t,o){"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},function(e,t,o){var r=o(5),n={};for(var i in r)r.hasOwnProperty(i)&&(n[r[i]]=i);var a=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var l in a)if(a.hasOwnProperty(l)){if(!("channels"in a[l]))throw new Error("missing channels property: "+l);if(!("labels"in a[l]))throw new Error("missing channel labels property: "+l);if(a[l].labels.length!==a[l].channels)throw new Error("channel and label counts mismatch: "+l);var s=a[l].channels,c=a[l].labels;delete a[l].channels,delete a[l].labels,Object.defineProperty(a[l],"channels",{value:s}),Object.defineProperty(a[l],"labels",{value:c})}a.rgb.hsl=function(e){var t,o,r=e[0]/255,n=e[1]/255,i=e[2]/255,a=Math.min(r,n,i),l=Math.max(r,n,i),s=l-a;return l===a?t=0:r===l?t=(n-i)/s:n===l?t=2+(i-r)/s:i===l&&(t=4+(r-n)/s),(t=Math.min(60*t,360))<0&&(t+=360),o=(a+l)/2,[t,100*(l===a?0:o<=.5?s/(l+a):s/(2-l-a)),100*o]},a.rgb.hsv=function(e){var t,o,r,n,i,a=e[0]/255,l=e[1]/255,s=e[2]/255,c=Math.max(a,l,s),u=c-Math.min(a,l,s),h=function(e){return(c-e)/6/u+.5};return 0===u?n=i=0:(i=u/c,t=h(a),o=h(l),r=h(s),a===c?n=r-o:l===c?n=1/3+t-r:s===c&&(n=2/3+o-t),n<0?n+=1:n>1&&(n-=1)),[360*n,100*i,100*c]},a.rgb.hwb=function(e){var t=e[0],o=e[1],r=e[2];return[a.rgb.hsl(e)[0],1/255*Math.min(t,Math.min(o,r))*100,100*(r=1-1/255*Math.max(t,Math.max(o,r)))]},a.rgb.cmyk=function(e){var t,o=e[0]/255,r=e[1]/255,n=e[2]/255;return[100*((1-o-(t=Math.min(1-o,1-r,1-n)))/(1-t)||0),100*((1-r-t)/(1-t)||0),100*((1-n-t)/(1-t)||0),100*t]},a.rgb.keyword=function(e){var t=n[e];if(t)return t;var o,i,a,l=1/0;for(var s in r)if(r.hasOwnProperty(s)){var c=r[s],u=(i=e,a=c,Math.pow(i[0]-a[0],2)+Math.pow(i[1]-a[1],2)+Math.pow(i[2]-a[2],2));u<l&&(l=u,o=s)}return o},a.keyword.rgb=function(e){return r[e]},a.rgb.xyz=function(e){var t=e[0]/255,o=e[1]/255,r=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(o=o>.04045?Math.pow((o+.055)/1.055,2.4):o/12.92)+.1805*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)),100*(.2126*t+.7152*o+.0722*r),100*(.0193*t+.1192*o+.9505*r)]},a.rgb.lab=function(e){var t=a.rgb.xyz(e),o=t[0],r=t[1],n=t[2];return r/=100,n/=108.883,o=(o/=95.047)>.008856?Math.pow(o,1/3):7.787*o+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(o-r),200*(r-(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116))]},a.hsl.rgb=function(e){var t,o,r,n,i,a=e[0]/360,l=e[1]/100,s=e[2]/100;if(0===l)return[i=255*s,i,i];t=2*s-(o=s<.5?s*(1+l):s+l-s*l),n=[0,0,0];for(var c=0;c<3;c++)(r=a+1/3*-(c-1))<0&&r++,r>1&&r--,i=6*r<1?t+6*(o-t)*r:2*r<1?o:3*r<2?t+(o-t)*(2/3-r)*6:t,n[c]=255*i;return n},a.hsl.hsv=function(e){var t=e[0],o=e[1]/100,r=e[2]/100,n=o,i=Math.max(r,.01);return o*=(r*=2)<=1?r:2-r,n*=i<=1?i:2-i,[t,100*(0===r?2*n/(i+n):2*o/(r+o)),(r+o)/2*100]},a.hsv.rgb=function(e){var t=e[0]/60,o=e[1]/100,r=e[2]/100,n=Math.floor(t)%6,i=t-Math.floor(t),a=255*r*(1-o),l=255*r*(1-o*i),s=255*r*(1-o*(1-i));switch(r*=255,n){case 0:return[r,s,a];case 1:return[l,r,a];case 2:return[a,r,s];case 3:return[a,l,r];case 4:return[s,a,r];case 5:return[r,a,l]}},a.hsv.hsl=function(e){var t,o,r,n=e[0],i=e[1]/100,a=e[2]/100,l=Math.max(a,.01);return r=(2-i)*a,o=i*l,[n,100*(o=(o/=(t=(2-i)*l)<=1?t:2-t)||0),100*(r/=2)]},a.hwb.rgb=function(e){var t,o,r,n,i,a,l,s=e[0]/360,c=e[1]/100,u=e[2]/100,h=c+u;switch(h>1&&(c/=h,u/=h),r=6*s-(t=Math.floor(6*s)),0!=(1&t)&&(r=1-r),n=c+r*((o=1-u)-c),t){default:case 6:case 0:i=o,a=n,l=c;break;case 1:i=n,a=o,l=c;break;case 2:i=c,a=o,l=n;break;case 3:i=c,a=n,l=o;break;case 4:i=n,a=c,l=o;break;case 5:i=o,a=c,l=n}return[255*i,255*a,255*l]},a.cmyk.rgb=function(e){var t=e[0]/100,o=e[1]/100,r=e[2]/100,n=e[3]/100;return[255*(1-Math.min(1,t*(1-n)+n)),255*(1-Math.min(1,o*(1-n)+n)),255*(1-Math.min(1,r*(1-n)+n))]},a.xyz.rgb=function(e){var t,o,r,n=e[0]/100,i=e[1]/100,a=e[2]/100;return o=-.9689*n+1.8758*i+.0415*a,r=.0557*n+-.204*i+1.057*a,t=(t=3.2406*n+-1.5372*i+-.4986*a)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:12.92*o,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,[255*(t=Math.min(Math.max(0,t),1)),255*(o=Math.min(Math.max(0,o),1)),255*(r=Math.min(Math.max(0,r),1))]},a.xyz.lab=function(e){var t=e[0],o=e[1],r=e[2];return o/=100,r/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(o=o>.008856?Math.pow(o,1/3):7.787*o+16/116)-16,500*(t-o),200*(o-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},a.lab.xyz=function(e){var t,o,r,n=e[0];t=e[1]/500+(o=(n+16)/116),r=o-e[2]/200;var i=Math.pow(o,3),a=Math.pow(t,3),l=Math.pow(r,3);return o=i>.008856?i:(o-16/116)/7.787,t=a>.008856?a:(t-16/116)/7.787,r=l>.008856?l:(r-16/116)/7.787,[t*=95.047,o*=100,r*=108.883]},a.lab.lch=function(e){var t,o=e[0],r=e[1],n=e[2];return(t=360*Math.atan2(n,r)/2/Math.PI)<0&&(t+=360),[o,Math.sqrt(r*r+n*n),t]},a.lch.lab=function(e){var t,o=e[0],r=e[1];return t=e[2]/360*2*Math.PI,[o,r*Math.cos(t),r*Math.sin(t)]},a.rgb.ansi16=function(e){var t=e[0],o=e[1],r=e[2],n=1 in arguments?arguments[1]:a.rgb.hsv(e)[2];if(0===(n=Math.round(n/50)))return 30;var i=30+(Math.round(r/255)<<2|Math.round(o/255)<<1|Math.round(t/255));return 2===n&&(i+=60),i},a.hsv.ansi16=function(e){return a.rgb.ansi16(a.hsv.rgb(e),e[2])},a.rgb.ansi256=function(e){var t=e[0],o=e[1],r=e[2];return t===o&&o===r?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(o/255*5)+Math.round(r/255*5)},a.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var o=.5*(1+~~(e>50));return[(1&t)*o*255,(t>>1&1)*o*255,(t>>2&1)*o*255]},a.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var o;return e-=16,[Math.floor(e/36)/5*255,Math.floor((o=e%36)/6)/5*255,o%6/5*255]},a.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},a.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var o=t[0];3===t[0].length&&(o=o.split("").map((function(e){return e+e})).join(""));var r=parseInt(o,16);return[r>>16&255,r>>8&255,255&r]},a.rgb.hcg=function(e){var t,o=e[0]/255,r=e[1]/255,n=e[2]/255,i=Math.max(Math.max(o,r),n),a=Math.min(Math.min(o,r),n),l=i-a;return t=l<=0?0:i===o?(r-n)/l%6:i===r?2+(n-o)/l:4+(o-r)/l+4,t/=6,[360*(t%=1),100*l,100*(l<1?a/(1-l):0)]},a.hsl.hcg=function(e){var t=e[1]/100,o=e[2]/100,r=1,n=0;return(r=o<.5?2*t*o:2*t*(1-o))<1&&(n=(o-.5*r)/(1-r)),[e[0],100*r,100*n]},a.hsv.hcg=function(e){var t=e[1]/100,o=e[2]/100,r=t*o,n=0;return r<1&&(n=(o-r)/(1-r)),[e[0],100*r,100*n]},a.hcg.rgb=function(e){var t=e[0]/360,o=e[1]/100,r=e[2]/100;if(0===o)return[255*r,255*r,255*r];var n,i=[0,0,0],a=t%1*6,l=a%1,s=1-l;switch(Math.floor(a)){case 0:i[0]=1,i[1]=l,i[2]=0;break;case 1:i[0]=s,i[1]=1,i[2]=0;break;case 2:i[0]=0,i[1]=1,i[2]=l;break;case 3:i[0]=0,i[1]=s,i[2]=1;break;case 4:i[0]=l,i[1]=0,i[2]=1;break;default:i[0]=1,i[1]=0,i[2]=s}return n=(1-o)*r,[255*(o*i[0]+n),255*(o*i[1]+n),255*(o*i[2]+n)]},a.hcg.hsv=function(e){var t=e[1]/100,o=t+e[2]/100*(1-t),r=0;return o>0&&(r=t/o),[e[0],100*r,100*o]},a.hcg.hsl=function(e){var t=e[1]/100,o=e[2]/100*(1-t)+.5*t,r=0;return o>0&&o<.5?r=t/(2*o):o>=.5&&o<1&&(r=t/(2*(1-o))),[e[0],100*r,100*o]},a.hcg.hwb=function(e){var t=e[1]/100,o=t+e[2]/100*(1-t);return[e[0],100*(o-t),100*(1-o)]},a.hwb.hcg=function(e){var t=e[1]/100,o=1-e[2]/100,r=o-t,n=0;return r<1&&(n=(o-r)/(1-r)),[e[0],100*r,100*n]},a.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},a.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},a.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},a.gray.hsl=a.gray.hsv=function(e){return[0,0,e[0]]},a.gray.hwb=function(e){return[0,100,e[0]]},a.gray.cmyk=function(e){return[0,0,0,e[0]]},a.gray.lab=function(e){return[e[0],0,0]},a.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),o=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(o.length)+o},a.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}},function(e,t,o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=a(o(8)),i=a(o(0));function a(e){return e&&e.__esModule?e:{default:e}}var l="colorpicker";i.default[l]=n.default,i.default.fn[l]=function(e){var t=Array.prototype.slice.call(arguments,1),o=1===this.length,a=null,s=this.each((function(){var s=(0,i.default)(this),c=s.data(l),u="object"===(void 0===e?"undefined":r(e))?e:{};c||(c=new n.default(this,u),s.data(l,c)),o&&(a=s,"string"==typeof e&&(a="colorpicker"===e?c:i.default.isFunction(c[e])?c[e].apply(c,t):c[e]))}));return o?a:s},i.default.fn[l].constructor=n.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=v(o(1)),i=v(o(3)),a=v(o(9)),l=v(o(0)),s=v(o(13)),c=v(o(14)),u=v(o(15)),h=v(o(22)),p=v(o(23)),f=v(o(24)),d=v(o(2));function v(e){return e&&e.__esModule?e:{default:e}}var g=0,y="undefined"!=typeof self?self:void 0,k=function(){function e(t,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),g+=1,this.id=g,this.lastEvent={alias:null,e:null},this.element=(0,l.default)(t).addClass("colorpicker-element").attr("data-colorpicker-id",this.id),this.options=l.default.extend(!0,{},i.default,o,this.element.data()),this.disabled=!1,this.extensions=[],this.container=!0===this.options.container||!0!==this.options.container&&!0===this.options.inline?this.element:this.options.container,this.container=!1!==this.container&&(0,l.default)(this.container),this.inputHandler=new u.default(this),this.colorHandler=new h.default(this),this.sliderHandler=new s.default(this),this.popupHandler=new c.default(this,y),this.pickerHandler=new p.default(this),this.addonHandler=new f.default(this),this.init(),(0,l.default)(l.default.proxy((function(){this.trigger("colorpickerCreate")}),this))}return r(e,[{key:"color",get:function(){return this.colorHandler.color}},{key:"format",get:function(){return this.colorHandler.format}},{key:"picker",get:function(){return this.pickerHandler.picker}}],[{key:"Color",get:function(){return d.default}},{key:"Extension",get:function(){return n.default}}]),r(e,[{key:"init",value:function(){this.addonHandler.bind(),this.inputHandler.bind(),this.initExtensions(),this.colorHandler.bind(),this.pickerHandler.bind(),this.sliderHandler.bind(),this.popupHandler.bind(),this.pickerHandler.attach(),this.update(),this.inputHandler.isDisabled()&&this.disable()}},{key:"initExtensions",value:function(){var t=this;Array.isArray(this.options.extensions)||(this.options.extensions=[]),this.options.debug&&this.options.extensions.push({name:"debugger"}),this.options.extensions.forEach((function(o){t.registerExtension(e.extensions[o.name.toLowerCase()],o.options||{})}))}},{key:"registerExtension",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=new e(this,t);return this.extensions.push(o),o}},{key:"destroy",value:function(){var e=this.color;this.sliderHandler.unbind(),this.inputHandler.unbind(),this.popupHandler.unbind(),this.colorHandler.unbind(),this.addonHandler.unbind(),this.pickerHandler.unbind(),this.element.removeClass("colorpicker-element").removeData("colorpicker").removeData("color").off(".colorpicker"),this.trigger("colorpickerDestroy",e)}},{key:"show",value:function(e){this.popupHandler.show(e)}},{key:"hide",value:function(e){this.popupHandler.hide(e)}},{key:"toggle",value:function(e){this.popupHandler.toggle(e)}},{key:"getValue",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=this.colorHandler.color;return(t=t instanceof d.default?t:e)instanceof d.default?t.string(this.format):t}},{key:"setValue",value:function(e){if(!this.isDisabled()){var t=this.colorHandler;t.hasColor()&&e&&t.color.equals(e)||!t.hasColor()&&!e||(t.color=e?t.createColor(e,this.options.autoInputFallback,this.options.autoHexInputFallback):null,this.trigger("colorpickerChange",t.color,e),this.update())}}},{key:"update",value:function(){this.colorHandler.hasColor()?this.inputHandler.update():this.colorHandler.assureColor(),this.addonHandler.update(),this.pickerHandler.update(),this.trigger("colorpickerUpdate")}},{key:"enable",value:function(){return this.inputHandler.enable(),this.disabled=!1,this.picker.removeClass("colorpicker-disabled"),this.trigger("colorpickerEnable"),!0}},{key:"disable",value:function(){return this.inputHandler.disable(),this.disabled=!0,this.picker.addClass("colorpicker-disabled"),this.trigger("colorpickerDisable"),!0}},{key:"isEnabled",value:function(){return!this.isDisabled()}},{key:"isDisabled",value:function(){return!0===this.disabled}},{key:"trigger",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.element.trigger({type:e,colorpicker:this,color:t||this.color,value:o||this.getValue()})}}]),e}();k.extensions=a.default,t.default=k,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Palette=t.Swatches=t.Preview=t.Debugger=void 0;var r=l(o(10)),n=l(o(11)),i=l(o(12)),a=l(o(4));function l(e){return e&&e.__esModule?e:{default:e}}t.Debugger=r.default,t.Preview=n.default,t.Swatches=i.default,t.Palette=a.default,t.default={debugger:r.default,preview:n.default,swatches:i.default,palette:a.default}},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=function e(t,o,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,o);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,o,r)}if("value"in n)return n.value;var a=n.get;return void 0!==a?a.call(r):void 0},i=l(o(1)),a=l(o(0));function l(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(this,t);var r=c(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,o));return r.eventCounter=0,r.colorpicker.inputHandler.hasInput()&&r.colorpicker.inputHandler.input.on("change.colorpicker-ext",a.default.proxy(r.onChangeInput,r)),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"log",value:function(e){for(var t,o=arguments.length,r=Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n];this.eventCounter+=1;var i="#"+this.eventCounter+": Colorpicker#"+this.colorpicker.id+" ["+e+"]";(t=console).debug.apply(t,[i].concat(r)),this.colorpicker.element.trigger({type:"colorpickerDebug",colorpicker:this.colorpicker,color:this.color,value:null,debug:{debugger:this,eventName:e,logArgs:r,logMessage:i}})}},{key:"resolveColor",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.log("resolveColor()",e,t),!1}},{key:"onCreate",value:function(e){return this.log("colorpickerCreate"),n(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onCreate",this).call(this,e)}},{key:"onDestroy",value:function(e){return this.log("colorpickerDestroy"),this.eventCounter=0,this.colorpicker.inputHandler.hasInput()&&this.colorpicker.inputHandler.input.off(".colorpicker-ext"),n(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onDestroy",this).call(this,e)}},{key:"onUpdate",value:function(e){this.log("colorpickerUpdate")}},{key:"onChangeInput",value:function(e){this.log("input:change.colorpicker",e.value,e.color)}},{key:"onChange",value:function(e){this.log("colorpickerChange",e.value,e.color)}},{key:"onInvalid",value:function(e){this.log("colorpickerInvalid",e.value,e.color)}},{key:"onHide",value:function(e){this.log("colorpickerHide"),this.eventCounter=0}},{key:"onShow",value:function(e){this.log("colorpickerShow")}},{key:"onDisable",value:function(e){this.log("colorpickerDisable")}},{key:"onEnable",value:function(e){this.log("colorpickerEnable")}}]),t}(i.default);t.default=u,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=function e(t,o,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,o);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,o,r)}if("value"in n)return n.value;var a=n.get;return void 0!==a?a.call(r):void 0},i=l(o(1)),a=l(o(0));function l(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u=function(e){function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(this,t);var r=c(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a.default.extend(!0,{},{template:'<div class="colorpicker-bar colorpicker-preview"><div /></div>',showText:!0,format:e.format},o)));return r.element=(0,a.default)(r.options.template),r.elementInner=r.element.find("div"),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"onCreate",value:function(e){n(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onCreate",this).call(this,e),this.colorpicker.picker.append(this.element)}},{key:"onUpdate",value:function(e){n(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onUpdate",this).call(this,e),e.color?(this.elementInner.css("backgroundColor",e.color.toRgbString()),this.options.showText&&(this.elementInner.html(e.color.string(this.options.format||this.colorpicker.format)),e.color.isDark()&&e.color.alpha>.5?this.elementInner.css("color","white"):this.elementInner.css("color","black"))):this.elementInner.css("backgroundColor",null).css("color",null).html("")}}]),t}(i.default);t.default=u,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=function e(t,o,r){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,o);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,o,r)}if("value"in n)return n.value;var a=n.get;return void 0!==a?a.call(r):void 0},i=l(o(4)),a=l(o(0));function l(e){return e&&e.__esModule?e:{default:e}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var u={barTemplate:'<div class="colorpicker-bar colorpicker-swatches">\n                    <div class="colorpicker-swatches--inner"></div>\n                </div>',swatchTemplate:'<i class="colorpicker-swatch"><i class="colorpicker-swatch--inner"></i></i>'},h=function(e){function t(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(this,t);var r=c(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a.default.extend(!0,{},u,o)));return r.element=null,r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"isEnabled",value:function(){return this.getLength()>0}},{key:"onCreate",value:function(e){n(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"onCreate",this).call(this,e),this.isEnabled()&&(this.element=(0,a.default)(this.options.barTemplate),this.load(),this.colorpicker.picker.append(this.element))}},{key:"load",value:function(){var e=this,t=this.colorpicker,o=this.element.find(".colorpicker-swatches--inner"),r=!0===this.options.namesAsValues&&!Array.isArray(this.colors);o.empty(),a.default.each(this.colors,(function(n,i){var l=(0,a.default)(e.options.swatchTemplate).attr("data-name",n).attr("data-value",i).attr("title",r?n+": "+i:i).on("mousedown.colorpicker touchstart.colorpicker",(function(e){var o=(0,a.default)(this);t.setValue(r?o.attr("data-name"):o.attr("data-value"))}));l.find(".colorpicker-swatch--inner").css("background-color",i),o.append(l)})),o.append((0,a.default)('<i class="colorpicker-clear"></i>'))}}]),t}(i.default);t.default=h,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),i=o(0),a=(r=i)&&r.__esModule?r:{default:r},l=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.colorpicker=t,this.currentSlider=null,this.mousePointer={left:0,top:0},this.onMove=a.default.proxy(this.defaultOnMove,this)}return n(e,[{key:"defaultOnMove",value:function(e,t){if(this.currentSlider){var o=this.currentSlider,r=this.colorpicker,n=r.colorHandler,i=n.hasColor()?n.color.getClone():n.getFallbackColor();o.guideStyle.left=t+"px",o.guideStyle.top=e+"px",o.callLeft&&i[o.callLeft](t/o.maxLeft),o.callTop&&i[o.callTop](e/o.maxTop),r.setValue(i),r.popupHandler.focus()}}},{key:"bind",value:function(){var e=this.colorpicker.options.horizontal?this.colorpicker.options.slidersHorz:this.colorpicker.options.sliders,t=[];for(var o in e)e.hasOwnProperty(o)&&t.push(e[o].selector);this.colorpicker.picker.find(t.join(", ")).on("mousedown.colorpicker touchstart.colorpicker",a.default.proxy(this.pressed,this))}},{key:"unbind",value:function(){(0,a.default)(this.colorpicker.picker).off({"mousemove.colorpicker":a.default.proxy(this.moved,this),"touchmove.colorpicker":a.default.proxy(this.moved,this),"mouseup.colorpicker":a.default.proxy(this.released,this),"touchend.colorpicker":a.default.proxy(this.released,this)})}},{key:"pressed",value:function(e){if(!this.colorpicker.isDisabled()){this.colorpicker.lastEvent.alias="pressed",this.colorpicker.lastEvent.e=e,!e.pageX&&!e.pageY&&e.originalEvent&&e.originalEvent.touches&&(e.pageX=e.originalEvent.touches[0].pageX,e.pageY=e.originalEvent.touches[0].pageY);var t=(0,a.default)(e.target).closest("div"),o=this.colorpicker.options.horizontal?this.colorpicker.options.slidersHorz:this.colorpicker.options.sliders;if(!t.is(".colorpicker")){for(var r in this.currentSlider=null,o)if(o.hasOwnProperty(r)){var n=o[r];if(t.is(n.selector)){this.currentSlider=a.default.extend({},n,{name:r});break}if(void 0!==n.childSelector&&t.is(n.childSelector)){this.currentSlider=a.default.extend({},n,{name:r}),t=t.parent();break}}var i=t.find(".colorpicker-guide").get(0);if(null!==this.currentSlider&&null!==i){var l=t.offset();this.currentSlider.guideStyle=i.style,this.currentSlider.left=e.pageX-l.left,this.currentSlider.top=e.pageY-l.top,this.mousePointer={left:e.pageX,top:e.pageY},(0,a.default)(this.colorpicker.picker).on({"mousemove.colorpicker":a.default.proxy(this.moved,this),"touchmove.colorpicker":a.default.proxy(this.moved,this),"mouseup.colorpicker":a.default.proxy(this.released,this),"touchend.colorpicker":a.default.proxy(this.released,this)}).trigger("mousemove")}}}}},{key:"moved",value:function(e){this.colorpicker.lastEvent.alias="moved",this.colorpicker.lastEvent.e=e,!e.pageX&&!e.pageY&&e.originalEvent&&e.originalEvent.touches&&(e.pageX=e.originalEvent.touches[0].pageX,e.pageY=e.originalEvent.touches[0].pageY),e.preventDefault();var t=Math.max(0,Math.min(this.currentSlider.maxLeft,this.currentSlider.left+((e.pageX||this.mousePointer.left)-this.mousePointer.left))),o=Math.max(0,Math.min(this.currentSlider.maxTop,this.currentSlider.top+((e.pageY||this.mousePointer.top)-this.mousePointer.top)));this.onMove(o,t)}},{key:"released",value:function(e){this.colorpicker.lastEvent.alias="released",this.colorpicker.lastEvent.e=e,(0,a.default)(this.colorpicker.picker).off({"mousemove.colorpicker":this.moved,"touchmove.colorpicker":this.moved,"mouseup.colorpicker":this.released,"touchend.colorpicker":this.released})}}]),e}();t.default=l,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=a(o(0)),i=a(o(3));function a(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(t,o){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.root=o,this.colorpicker=t,this.popoverTarget=null,this.popoverTip=null,this.clicking=!1,this.hidding=!1,this.showing=!1}return r(e,[{key:"bind",value:function(){var e=this.colorpicker;e.options.inline?e.picker.addClass("colorpicker-inline colorpicker-visible"):(e.picker.addClass("colorpicker-popup colorpicker-hidden"),(this.hasInput||this.hasAddon)&&(e.options.popover&&this.createPopover(),this.hasAddon&&(this.addon.attr("tabindex")||this.addon.attr("tabindex",0),this.addon.on({"mousedown.colorpicker touchstart.colorpicker":n.default.proxy(this.toggle,this)}),this.addon.on({"focus.colorpicker":n.default.proxy(this.show,this)}),this.addon.on({"focusout.colorpicker":n.default.proxy(this.hide,this)})),this.hasInput&&!this.hasAddon&&(this.input.on({"mousedown.colorpicker touchstart.colorpicker":n.default.proxy(this.show,this),"focus.colorpicker":n.default.proxy(this.show,this)}),this.input.on({"focusout.colorpicker":n.default.proxy(this.hide,this)})),(0,n.default)(this.root).on("resize.colorpicker",n.default.proxy(this.reposition,this))))}},{key:"unbind",value:function(){this.hasInput&&(this.input.off({"mousedown.colorpicker touchstart.colorpicker":n.default.proxy(this.show,this),"focus.colorpicker":n.default.proxy(this.show,this)}),this.input.off({"focusout.colorpicker":n.default.proxy(this.hide,this)})),this.hasAddon&&(this.addon.off({"mousedown.colorpicker touchstart.colorpicker":n.default.proxy(this.toggle,this)}),this.addon.off({"focus.colorpicker":n.default.proxy(this.show,this)}),this.addon.off({"focusout.colorpicker":n.default.proxy(this.hide,this)})),this.popoverTarget&&this.popoverTarget.popover("dispose"),(0,n.default)(this.root).off("resize.colorpicker",n.default.proxy(this.reposition,this)),(0,n.default)(this.root.document).off("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.hide,this)),(0,n.default)(this.root.document).off("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.onClickingInside,this))}},{key:"isClickingInside",value:function(e){return!!e&&(this.isOrIsInside(this.popoverTip,e.currentTarget)||this.isOrIsInside(this.popoverTip,e.target)||this.isOrIsInside(this.colorpicker.picker,e.currentTarget)||this.isOrIsInside(this.colorpicker.picker,e.target))}},{key:"isOrIsInside",value:function(e,t){return!(!e||!t)&&((t=(0,n.default)(t)).is(e)||e.find(t).length>0)}},{key:"onClickingInside",value:function(e){this.clicking=this.isClickingInside(e)}},{key:"createPopover",value:function(){var e=this.colorpicker;this.popoverTarget=this.hasAddon?this.addon:this.input,e.picker.addClass("colorpicker-bs-popover-content"),this.popoverTarget.popover(n.default.extend(!0,{},i.default.popover,e.options.popover,{trigger:"manual",content:e.picker,html:!0}));var t=window.bootstrap&&window.bootstrap.Popover&&window.bootstrap.Popover.getInstance;this.popoverTip=t?(0,n.default)(bootstrap.Popover.getInstance(this.popoverTarget[0]).getTipElement()):(0,n.default)(this.popoverTarget.popover("getTipElement").data("bs.popover").tip),this.popoverTip.addClass("colorpicker-bs-popover"),this.popoverTarget.on("shown.bs.popover",n.default.proxy(this.fireShow,this)),this.popoverTarget.on("hidden.bs.popover",n.default.proxy(this.fireHide,this))}},{key:"reposition",value:function(e){this.popoverTarget&&this.isVisible()&&this.popoverTarget.popover("update")}},{key:"toggle",value:function(e){this.isVisible()?this.hide(e):this.show(e)}},{key:"show",value:function(e){if(!(this.isVisible()||this.showing||this.hidding)){this.showing=!0,this.hidding=!1,this.clicking=!1;var t=this.colorpicker;t.lastEvent.alias="show",t.lastEvent.e=e,e&&(!this.hasInput||"color"===this.input.attr("type"))&&e&&e.preventDefault&&(e.stopPropagation(),e.preventDefault()),this.isPopover&&(0,n.default)(this.root).on("resize.colorpicker",n.default.proxy(this.reposition,this)),t.picker.addClass("colorpicker-visible").removeClass("colorpicker-hidden"),this.popoverTarget?this.popoverTarget.popover("show"):this.fireShow()}}},{key:"fireShow",value:function(){this.hidding=!1,this.showing=!1,this.isPopover&&((0,n.default)(this.root.document).on("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.hide,this)),(0,n.default)(this.root.document).on("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.onClickingInside,this))),this.colorpicker.trigger("colorpickerShow")}},{key:"hide",value:function(e){if(!(this.isHidden()||this.showing||this.hidding)){var t=this.colorpicker,o=this.clicking||this.isClickingInside(e);this.hidding=!0,this.showing=!1,this.clicking=!1,t.lastEvent.alias="hide",t.lastEvent.e=e,o?this.hidding=!1:this.popoverTarget?this.popoverTarget.popover("hide"):this.fireHide()}}},{key:"fireHide",value:function(){this.hidding=!1,this.showing=!1;var e=this.colorpicker;e.picker.addClass("colorpicker-hidden").removeClass("colorpicker-visible"),(0,n.default)(this.root).off("resize.colorpicker",n.default.proxy(this.reposition,this)),(0,n.default)(this.root.document).off("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.hide,this)),(0,n.default)(this.root.document).off("mousedown.colorpicker touchstart.colorpicker",n.default.proxy(this.onClickingInside,this)),e.trigger("colorpickerHide")}},{key:"focus",value:function(){return this.hasAddon?this.addon.focus():!!this.hasInput&&this.input.focus()}},{key:"isVisible",value:function(){return this.colorpicker.picker.hasClass("colorpicker-visible")&&!this.colorpicker.picker.hasClass("colorpicker-hidden")}},{key:"isHidden",value:function(){return this.colorpicker.picker.hasClass("colorpicker-hidden")&&!this.colorpicker.picker.hasClass("colorpicker-visible")}},{key:"input",get:function(){return this.colorpicker.inputHandler.input}},{key:"hasInput",get:function(){return this.colorpicker.inputHandler.hasInput()}},{key:"addon",get:function(){return this.colorpicker.addonHandler.addon}},{key:"hasAddon",get:function(){return this.colorpicker.addonHandler.hasAddon()}},{key:"isPopover",get:function(){return!this.colorpicker.options.inline&&!!this.popoverTip}}]),e}();t.default=l,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=a(o(0)),i=a(o(2));function a(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.colorpicker=t,this.input=this.colorpicker.element.is("input")?this.colorpicker.element:!!this.colorpicker.options.input&&this.colorpicker.element.find(this.colorpicker.options.input),this.input&&0===this.input.length&&(this.input=!1),this._initValue()}return r(e,[{key:"bind",value:function(){this.hasInput()&&(this.input.on({"keyup.colorpicker":n.default.proxy(this.onkeyup,this)}),this.input.on({"change.colorpicker":n.default.proxy(this.onchange,this)}))}},{key:"unbind",value:function(){this.hasInput()&&this.input.off(".colorpicker")}},{key:"_initValue",value:function(){if(this.hasInput()){var e="";[this.input.val(),this.input.data("color"),this.input.attr("data-color")].map((function(t){t&&""===e&&(e=t)})),e instanceof i.default?e=this.getFormattedColor(e.string(this.colorpicker.format)):"string"==typeof e||e instanceof String||(e=""),this.input.prop("value",e)}}},{key:"getValue",value:function(){return!!this.hasInput()&&this.input.val()}},{key:"setValue",value:function(e){if(this.hasInput()){var t=this.input.prop("value");(e=e||"")!==(t||"")&&(this.input.prop("value",e),this.input.trigger({type:"change",colorpicker:this.colorpicker,color:this.colorpicker.color,value:e}))}}},{key:"getFormattedColor",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return(e=e||this.colorpicker.colorHandler.getColorString())?(e=this.colorpicker.colorHandler.resolveColorDelegate(e,!1),!1===this.colorpicker.options.useHashPrefix&&(e=e.replace(/^#/g,"")),e):""}},{key:"hasInput",value:function(){return!1!==this.input}},{key:"isEnabled",value:function(){return this.hasInput()&&!this.isDisabled()}},{key:"isDisabled",value:function(){return this.hasInput()&&!0===this.input.prop("disabled")}},{key:"disable",value:function(){this.hasInput()&&this.input.prop("disabled",!0)}},{key:"enable",value:function(){this.hasInput()&&this.input.prop("disabled",!1)}},{key:"update",value:function(){this.hasInput()&&(!1===this.colorpicker.options.autoInputFallback&&this.colorpicker.colorHandler.isInvalidColor()||this.setValue(this.getFormattedColor()))}},{key:"onchange",value:function(e){this.colorpicker.lastEvent.alias="input.change",this.colorpicker.lastEvent.e=e;var t=this.getValue();t!==e.value&&this.colorpicker.setValue(t)}},{key:"onkeyup",value:function(e){this.colorpicker.lastEvent.alias="input.keyup",this.colorpicker.lastEvent.e=e;var t=this.getValue();t!==e.value&&this.colorpicker.setValue(t)}}]),e}();t.default=l,e.exports=t.default},function(e,t,o){"use strict";var r=o(17),n=o(20),i=[].slice,a=["keyword","gray","hex"],l={};Object.keys(n).forEach((function(e){l[i.call(n[e].labels).sort().join("")]=e}));var s={};function c(e,t){if(!(this instanceof c))return new c(e,t);if(t&&t in a&&(t=null),t&&!(t in n))throw new Error("Unknown model: "+t);var o,u;if(null==e)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(e instanceof c)this.model=e.model,this.color=e.color.slice(),this.valpha=e.valpha;else if("string"==typeof e){var h=r.get(e);if(null===h)throw new Error("Unable to parse color from string: "+e);this.model=h.model,u=n[this.model].channels,this.color=h.value.slice(0,u),this.valpha="number"==typeof h.value[u]?h.value[u]:1}else if(e.length){this.model=t||"rgb",u=n[this.model].channels;var p=i.call(e,0,u);this.color=f(p,u),this.valpha="number"==typeof e[u]?e[u]:1}else if("number"==typeof e)e&=16777215,this.model="rgb",this.color=[e>>16&255,e>>8&255,255&e],this.valpha=1;else{this.valpha=1;var d=Object.keys(e);"alpha"in e&&(d.splice(d.indexOf("alpha"),1),this.valpha="number"==typeof e.alpha?e.alpha:0);var v=d.sort().join("");if(!(v in l))throw new Error("Unable to parse color from object: "+JSON.stringify(e));this.model=l[v];var g=n[this.model].labels,y=[];for(o=0;o<g.length;o++)y.push(e[g[o]]);this.color=f(y)}if(s[this.model])for(u=n[this.model].channels,o=0;o<u;o++){var k=s[this.model][o];k&&(this.color[o]=k(this.color[o]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}function u(e,t,o){return(e=Array.isArray(e)?e:[e]).forEach((function(e){(s[e]||(s[e]=[]))[t]=o})),e=e[0],function(r){var n;return arguments.length?(o&&(r=o(r)),(n=this[e]()).color[t]=r,n):(n=this[e]().color[t],o&&(n=o(n)),n)}}function h(e){return function(t){return Math.max(0,Math.min(e,t))}}function p(e){return Array.isArray(e)?e:[e]}function f(e,t){for(var o=0;o<t;o++)"number"!=typeof e[o]&&(e[o]=0);return e}c.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(e){var t=this.model in r.to?this:this.rgb(),o=1===(t=t.round("number"==typeof e?e:1)).valpha?t.color:t.color.concat(this.valpha);return r.to[t.model](o)},percentString:function(e){var t=this.rgb().round("number"==typeof e?e:1),o=1===t.valpha?t.color:t.color.concat(this.valpha);return r.to.rgb.percent(o)},array:function(){return 1===this.valpha?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var e={},t=n[this.model].channels,o=n[this.model].labels,r=0;r<t;r++)e[o[r]]=this.color[r];return 1!==this.valpha&&(e.alpha=this.valpha),e},unitArray:function(){var e=this.rgb().color;return e[0]/=255,e[1]/=255,e[2]/=255,1!==this.valpha&&e.push(this.valpha),e},unitObject:function(){var e=this.rgb().object();return e.r/=255,e.g/=255,e.b/=255,1!==this.valpha&&(e.alpha=this.valpha),e},round:function(e){return e=Math.max(e||0,0),new c(this.color.map(function(e){return function(t){return function(e,t){return Number(e.toFixed(t))}(t,e)}}(e)).concat(this.valpha),this.model)},alpha:function(e){return arguments.length?new c(this.color.concat(Math.max(0,Math.min(1,e))),this.model):this.valpha},red:u("rgb",0,h(255)),green:u("rgb",1,h(255)),blue:u("rgb",2,h(255)),hue:u(["hsl","hsv","hsl","hwb","hcg"],0,(function(e){return(e%360+360)%360})),saturationl:u("hsl",1,h(100)),lightness:u("hsl",2,h(100)),saturationv:u("hsv",1,h(100)),value:u("hsv",2,h(100)),chroma:u("hcg",1,h(100)),gray:u("hcg",2,h(100)),white:u("hwb",1,h(100)),wblack:u("hwb",2,h(100)),cyan:u("cmyk",0,h(100)),magenta:u("cmyk",1,h(100)),yellow:u("cmyk",2,h(100)),black:u("cmyk",3,h(100)),x:u("xyz",0,h(100)),y:u("xyz",1,h(100)),z:u("xyz",2,h(100)),l:u("lab",0,h(100)),a:u("lab",1),b:u("lab",2),keyword:function(e){return arguments.length?new c(e):n[this.model].keyword(this.color)},hex:function(e){return arguments.length?new c(e):r.to.hex(this.rgb().round().color)},rgbNumber:function(){var e=this.rgb().color;return(255&e[0])<<16|(255&e[1])<<8|255&e[2]},luminosity:function(){for(var e=this.rgb().color,t=[],o=0;o<e.length;o++){var r=e[o]/255;t[o]=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),o=e.luminosity();return t>o?(t+.05)/(o+.05):(o+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},isDark:function(){var e=this.rgb().color;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},isLight:function(){return!this.isDark()},negate:function(){for(var e=this.rgb(),t=0;t<3;t++)e.color[t]=255-e.color[t];return e},lighten:function(e){var t=this.hsl();return t.color[2]+=t.color[2]*e,t},darken:function(e){var t=this.hsl();return t.color[2]-=t.color[2]*e,t},saturate:function(e){var t=this.hsl();return t.color[1]+=t.color[1]*e,t},desaturate:function(e){var t=this.hsl();return t.color[1]-=t.color[1]*e,t},whiten:function(e){var t=this.hwb();return t.color[1]+=t.color[1]*e,t},blacken:function(e){var t=this.hwb();return t.color[2]+=t.color[2]*e,t},grayscale:function(){var e=this.rgb().color,t=.3*e[0]+.59*e[1]+.11*e[2];return c.rgb(t,t,t)},fade:function(e){return this.alpha(this.valpha-this.valpha*e)},opaquer:function(e){return this.alpha(this.valpha+this.valpha*e)},rotate:function(e){var t=this.hsl(),o=t.color[0];return o=(o=(o+e)%360)<0?360+o:o,t.color[0]=o,t},mix:function(e,t){if(!e||!e.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof e);var o=e.rgb(),r=this.rgb(),n=void 0===t?.5:t,i=2*n-1,a=o.alpha()-r.alpha(),l=((i*a==-1?i:(i+a)/(1+i*a))+1)/2,s=1-l;return c.rgb(l*o.red()+s*r.red(),l*o.green()+s*r.green(),l*o.blue()+s*r.blue(),o.alpha()*n+r.alpha()*(1-n))}},Object.keys(n).forEach((function(e){if(-1===a.indexOf(e)){var t=n[e].channels;c.prototype[e]=function(){if(this.model===e)return new c(this);if(arguments.length)return new c(arguments,e);var o="number"==typeof arguments[t]?t:this.valpha;return new c(p(n[this.model][e].raw(this.color)).concat(o),e)},c[e]=function(o){return"number"==typeof o&&(o=f(i.call(arguments),t)),new c(o,e)}}})),e.exports=c},function(e,t,o){var r=o(5),n=o(18),i={};for(var a in r)r.hasOwnProperty(a)&&(i[r[a]]=a);var l=e.exports={to:{},get:{}};function s(e,t,o){return Math.min(Math.max(t,e),o)}function c(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}l.get=function(e){var t,o;switch(e.substring(0,3).toLowerCase()){case"hsl":t=l.get.hsl(e),o="hsl";break;case"hwb":t=l.get.hwb(e),o="hwb";break;default:t=l.get.rgb(e),o="rgb"}return t?{model:o,value:t}:null},l.get.rgb=function(e){if(!e)return null;var t,o,n,i=[0,0,0,1];if(t=e.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(n=t[2],t=t[1],o=0;o<3;o++){var a=2*o;i[o]=parseInt(t.slice(a,a+2),16)}n&&(i[3]=Math.round(parseInt(n,16)/255*100)/100)}else if(t=e.match(/^#([a-f0-9]{3,4})$/i)){for(n=(t=t[1])[3],o=0;o<3;o++)i[o]=parseInt(t[o]+t[o],16);n&&(i[3]=Math.round(parseInt(n+n,16)/255*100)/100)}else if(t=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)){for(o=0;o<3;o++)i[o]=parseInt(t[o+1],0);t[4]&&(i[3]=parseFloat(t[4]))}else{if(!(t=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/)))return(t=e.match(/(\D+)/))?"transparent"===t[1]?[0,0,0,0]:(i=r[t[1]])?(i[3]=1,i):null:null;for(o=0;o<3;o++)i[o]=Math.round(2.55*parseFloat(t[o+1]));t[4]&&(i[3]=parseFloat(t[4]))}for(o=0;o<3;o++)i[o]=s(i[o],0,255);return i[3]=s(i[3],0,1),i},l.get.hsl=function(e){if(!e)return null;var t=e.match(/^hsla?\(\s*([+-]?(?:\d*\.)?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/);if(t){var o=parseFloat(t[4]);return[(parseFloat(t[1])+360)%360,s(parseFloat(t[2]),0,100),s(parseFloat(t[3]),0,100),s(isNaN(o)?1:o,0,1)]}return null},l.get.hwb=function(e){if(!e)return null;var t=e.match(/^hwb\(\s*([+-]?\d*[\.]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/);if(t){var o=parseFloat(t[4]);return[(parseFloat(t[1])%360+360)%360,s(parseFloat(t[2]),0,100),s(parseFloat(t[3]),0,100),s(isNaN(o)?1:o,0,1)]}return null},l.to.hex=function(){var e=n(arguments);return"#"+c(e[0])+c(e[1])+c(e[2])+(e[3]<1?c(Math.round(255*e[3])):"")},l.to.rgb=function(){var e=n(arguments);return e.length<4||1===e[3]?"rgb("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+")":"rgba("+Math.round(e[0])+", "+Math.round(e[1])+", "+Math.round(e[2])+", "+e[3]+")"},l.to.rgb.percent=function(){var e=n(arguments),t=Math.round(e[0]/255*100),o=Math.round(e[1]/255*100),r=Math.round(e[2]/255*100);return e.length<4||1===e[3]?"rgb("+t+"%, "+o+"%, "+r+"%)":"rgba("+t+"%, "+o+"%, "+r+"%, "+e[3]+")"},l.to.hsl=function(){var e=n(arguments);return e.length<4||1===e[3]?"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)":"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+e[3]+")"},l.to.hwb=function(){var e=n(arguments),t="";return e.length>=4&&1!==e[3]&&(t=", "+e[3]),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+t+")"},l.to.keyword=function(e){return i[e.slice(0,3)]}},function(e,t,o){"use strict";var r=o(19),n=Array.prototype.concat,i=Array.prototype.slice,a=e.exports=function(e){for(var t=[],o=0,a=e.length;o<a;o++){var l=e[o];r(l)?t=n.call(t,i.call(l)):t.push(l)}return t};a.wrap=function(e){return function(){return e(a(arguments))}}},function(e,t,o){"use strict";e.exports=function(e){return!!e&&(e instanceof Array||Array.isArray(e)||e.length>=0&&e.splice instanceof Function)}},function(e,t,o){var r=o(6),n=o(21),i={};Object.keys(r).forEach((function(e){i[e]={},Object.defineProperty(i[e],"channels",{value:r[e].channels}),Object.defineProperty(i[e],"labels",{value:r[e].labels});var t=n(e);Object.keys(t).forEach((function(o){var r=t[o];i[e][o]=function(e){var t=function(t){if(null==t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var o=e(t);if("object"==typeof o)for(var r=o.length,n=0;n<r;n++)o[n]=Math.round(o[n]);return o};return"conversion"in e&&(t.conversion=e.conversion),t}(r),i[e][o].raw=function(e){var t=function(t){return null==t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)}))})),e.exports=i},function(e,t,o){var r=o(6);function n(e){var t=function(){for(var e={},t=Object.keys(r),o=t.length,n=0;n<o;n++)e[t[n]]={distance:-1,parent:null};return e}(),o=[e];for(t[e].distance=0;o.length;)for(var n=o.pop(),i=Object.keys(r[n]),a=i.length,l=0;l<a;l++){var s=i[l],c=t[s];-1===c.distance&&(c.distance=t[n].distance+1,c.parent=n,o.unshift(s))}return t}function i(e,t){return function(o){return t(e(o))}}function a(e,t){for(var o=[t[e].parent,e],n=r[t[e].parent][e],a=t[e].parent;t[a].parent;)o.unshift(t[a].parent),n=i(r[t[a].parent][a],n),a=t[a].parent;return n.conversion=o,n}e.exports=function(e){for(var t=n(e),o={},r=Object.keys(t),i=r.length,l=0;l<i;l++){var s=r[l];null!==t[s].parent&&(o[s]=a(s,t))}return o}},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=a(o(0)),i=a(o(2));function a(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.colorpicker=t}return r(e,[{key:"bind",value:function(){this.colorpicker.options.color?this.color=this.createColor(this.colorpicker.options.color):!this.color&&this.colorpicker.inputHandler.getValue()&&(this.color=this.createColor(this.colorpicker.inputHandler.getValue(),this.colorpicker.options.autoInputFallback))}},{key:"unbind",value:function(){this.colorpicker.element.removeData("color")}},{key:"getColorString",value:function(){return this.hasColor()?this.color.string(this.format):""}},{key:"setColorString",value:function(e){var t=e?this.createColor(e):null;this.color=t||null}},{key:"createColor",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!t&&!o,n=new i.default(this.resolveColorDelegate(e),this.format,r);return n.isValid()||(t&&(n=this.getFallbackColor()),this.colorpicker.trigger("colorpickerInvalid",n,e)),this.isAlphaEnabled()||(n.alpha=1),n}},{key:"getFallbackColor",value:function(){if(this.fallback&&this.fallback===this.color)return this.color;var e=this.resolveColorDelegate(this.fallback),t=new i.default(e,this.format);return t.isValid()?t:(console.warn("The fallback color is invalid. Falling back to the previous color or black if any."),this.color?this.color:new i.default("#000000",this.format))}},{key:"assureColor",value:function(){return this.hasColor()||(this.color=this.getFallbackColor()),this.color}},{key:"resolveColorDelegate",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=!1;return n.default.each(this.colorpicker.extensions,(function(r,n){!1===o&&(o=n.resolveColor(e,t))})),o||e}},{key:"isInvalidColor",value:function(){return!this.hasColor()||!this.color.isValid()}},{key:"isAlphaEnabled",value:function(){return!1!==this.colorpicker.options.useAlpha}},{key:"hasColor",value:function(){return this.color instanceof i.default}},{key:"fallback",get:function(){return this.colorpicker.options.fallbackColor?this.colorpicker.options.fallbackColor:this.hasColor()?this.color:null}},{key:"format",get:function(){return this.colorpicker.options.format?this.colorpicker.options.format:this.hasColor()&&this.color.hasTransparency()&&this.color.format.match(/^hex/)?this.isAlphaEnabled()?"rgba":"hex":this.hasColor()?this.color.format:"rgb"}},{key:"color",get:function(){return this.colorpicker.element.data("color")},set:function(e){this.colorpicker.element.data("color",e),e instanceof i.default&&"auto"===this.colorpicker.options.format&&(this.colorpicker.options.format=this.color.format)}}]),e}();t.default=l,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),i=o(0),a=(r=i)&&r.__esModule?r:{default:r},l=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.colorpicker=t,this.picker=null}return n(e,[{key:"bind",value:function(){var e=this.picker=(0,a.default)(this.options.template);this.options.customClass&&e.addClass(this.options.customClass),this.options.horizontal&&e.addClass("colorpicker-horizontal"),this._supportsAlphaBar()?(this.options.useAlpha=!0,e.addClass("colorpicker-with-alpha")):this.options.useAlpha=!1}},{key:"attach",value:function(){var e=this.colorpicker.container?this.colorpicker.container:null;e&&this.picker.appendTo(e)}},{key:"unbind",value:function(){this.picker.remove()}},{key:"_supportsAlphaBar",value:function(){return(this.options.useAlpha||this.colorpicker.colorHandler.hasColor()&&this.color.hasTransparency())&&!1!==this.options.useAlpha&&(!this.options.format||this.options.format&&!this.options.format.match(/^hex([36])?$/i))}},{key:"update",value:function(){if(this.colorpicker.colorHandler.hasColor()){var e=!0!==this.options.horizontal,t=e?this.options.sliders:this.options.slidersHorz,o=this.picker.find(".colorpicker-saturation .colorpicker-guide"),r=this.picker.find(".colorpicker-hue .colorpicker-guide"),n=this.picker.find(".colorpicker-alpha .colorpicker-guide"),i=this.color.toHsvaRatio();r.length&&r.css(e?"top":"left",(e?t.hue.maxTop:t.hue.maxLeft)*(1-i.h)),n.length&&n.css(e?"top":"left",(e?t.alpha.maxTop:t.alpha.maxLeft)*(1-i.a)),o.length&&o.css({top:t.saturation.maxTop-i.v*t.saturation.maxTop,left:i.s*t.saturation.maxLeft}),this.picker.find(".colorpicker-saturation").css("backgroundColor",this.color.getCloneHueOnly().toHexString());var a=this.color.toHexString(),l="";l=this.options.horizontal?"linear-gradient(to right, "+a+" 0%, transparent 100%)":"linear-gradient(to bottom, "+a+" 0%, transparent 100%)",this.picker.find(".colorpicker-alpha-color").css("background",l)}}},{key:"options",get:function(){return this.colorpicker.options}},{key:"color",get:function(){return this.colorpicker.colorHandler.color}}]),e}();t.default=l,e.exports=t.default},function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var o=0;o<t.length;o++){var r=t[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,o,r){return o&&e(t.prototype,o),r&&e(t,r),t}}(),n=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.colorpicker=t,this.addon=null}return r(e,[{key:"hasAddon",value:function(){return!!this.addon}},{key:"bind",value:function(){this.addon=this.colorpicker.options.addon?this.colorpicker.element.find(this.colorpicker.options.addon):null,this.addon&&0===this.addon.length&&(this.addon=null)}},{key:"unbind",value:function(){this.hasAddon()&&this.addon.off(".colorpicker")}},{key:"update",value:function(){if(this.colorpicker.colorHandler.hasColor()&&this.hasAddon()){var e={background:this.colorpicker.colorHandler.getColorString()},t=this.addon.find("i").eq(0);t.length>0?t.css(e):this.addon.css(e)}}}]),e}();t.default=n,e.exports=t.default}])},"object"==typeof r?e.exports=i(o(9567)):"function"==typeof t&&t.amd?t("bootstrap-colorpicker",["jquery"],i):"object"==typeof r?r["bootstrap-colorpicker"]=i(o(9567)):n["bootstrap-colorpicker"]=i(n.jQuery)}).call(window,!1,!1)},9567:e=>{"use strict";e.exports=window.jQuery}},t={};function o(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,o),i.exports}o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};(()=>{"use strict";o.r(r);o(5933);
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const{$:e}=window,t=function(){e('.colorpicker input[type="text"]').each(((t,o)=>{e(o).colorpicker()}))};
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
const n=new(o(7187).EventEmitter),{$:i}=window;const a=class{constructor(e={}){const t=e||{};this.localeItemSelector=t.localeItemSelector||".js-locale-item",this.localeButtonSelector=t.localeButtonSelector||".js-locale-btn",this.localeInputSelector=t.localeInputSelector||".js-locale-input",this.selectedLocale=i(this.localeItemSelector).data("locale"),i("body").on("click",this.localeItemSelector,this.toggleLanguage.bind(this)),n.on("languageSelected",this.toggleInputs.bind(this))}refreshInputs(e){this.selectedLocale&&n.emit("languageSelected",{selectedLocale:this.selectedLocale,form:e})}toggleLanguage(e){const t=i(e.target),o=t.closest("form");this.selectedLocale=t.data("locale"),this.refreshInputs(o)}toggleInputs(e){const{form:t}=e;this.selectedLocale=e.selectedLocale;const o=t.find(this.localeButtonSelector),r=o.data("change-language-url");o.text(this.selectedLocale.toUpperCase()),t.find(this.localeInputSelector).addClass("d-none"),t.find(`${this.localeInputSelector}.js-locale-${this.selectedLocale}`).removeClass("d-none"),r&&this.saveSelectedLanguage(r,this.selectedLocale)}saveSelectedLanguage(e,t){i.post({url:e,data:{language_iso_code:t}})}},{$:l}=window;l((()=>{t(),new a}))})(),window.order_return_states_form=r})();