/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

import FormSubmitButton from '@components/form-submit-button';
import SupplierMap from './supplier-map';

const {$} = window;

$(document).ready(() => {
  new window.prestashop.component.ChoiceTree('#supplier_shop_association').enableAutoCheckChildren();
  new window.prestashop.component.CountryStateSelectionToggler(
    SupplierMap.supplierCountrySelect,
    SupplierMap.supplierStateSelect,
    SupplierMap.supplierStateBlock,
  );
  new window.prestashop.component.CountryDniRequiredToggler(
    SupplierMap.supplierCountrySelect,
    SupplierMap.supplierDniInput,
    SupplierMap.supplierDniInputLabel,
  );

  window.prestashop.component.initComponents(
    [
      'TinyMCEEditor',
      'TranslatableInput',
      'TranslatableField',
    ],
  );

  new window.prestashop.component.TaggableField({
    tokenFieldSelector: 'input.js-taggable-field',
    options: {
      createTokensOnBlur: true,
    },
  });

  new FormSubmitButton();
});
