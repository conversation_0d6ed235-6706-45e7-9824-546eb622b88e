/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
import ComponentsMap from '@components/components-map';

const {$} = window;

/**
 * MultipleChoiceTable is responsible for managing common actions in multiple choice table form type
 */
export default class MultipleChoiceTable {
  /**
   * Init constructor
   */
  constructor() {
    $(document).on(
      'click',
      ComponentsMap.multipleChoiceTable.selectColumn,
      (e: JQueryEventObject) => this.handleSelectColumn(e),
    );
  }

  /**
   * Check/uncheck all boxes in column
   *
   * @param {Event} event
   */
  handleSelectColumn(event: JQueryEventObject): void {
    event.preventDefault();

    const $selectColumnBtn = $(event.target);
    const checked = $selectColumnBtn.data('column-checked');
    $selectColumnBtn.data('column-checked', !checked);

    const $table = $selectColumnBtn.closest('table');

    $table
      .find(
        ComponentsMap.multipleChoiceTable.selectColumnCheckbox(
          $selectColumnBtn.data('column-num'),
        ),
      )
      .prop('checked', !checked);
  }
}
