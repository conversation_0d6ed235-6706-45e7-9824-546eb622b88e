/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

const {$} = window;

/**
 * Responsible for opening another page with specified url.
 * For example used in 'Save and preview' cms page create/edit actions.
 *
 * Usage: In selector element attr 'data-preview-url' provide page url.
 * The page will be opened once provided 'open_preview' parameter in query url
 */
export default class PreviewOpener {
  previewUrl: string;

  constructor(previewUrlSelector: string) {
    this.previewUrl = $(previewUrlSelector).data('preview-url');
    this.open();
  }

  /**
   * Opens new page of provided url
   *
   * @private
   */
  private open(): void {
    const urlParams = new URLSearchParams(window.location.search);

    if (this.previewUrl && urlParams.has('open_preview')) {
      window.open(this.previewUrl, '_blank');
    }
  }
}
