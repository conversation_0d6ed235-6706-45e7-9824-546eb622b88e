{"base_url": "", "routes": {"admin_common_notifications": {"tokens": [["text", "/common/notifications"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_product_form": {"tokens": [["variable", "/", "\\d+", "id"], ["text", "/sell/catalog/products"]], "defaults": [], "requirements": {"id": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_feature_get_feature_values": {"tokens": [["variable", "/", "\\d+", "idFeature"], ["text", "/sell/catalog/products/features"]], "defaults": {"idFeature": 0}, "requirements": {"idFeature": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_combinations": {"tokens": [["text", "/combinations"], ["variable", "/", "[^/]++", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_combinations_ids": {"tokens": [["text", "/combinations/ids"], ["variable", "/", "[^/]++", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_combinations_update_combination_from_listing": {"tokens": [["text", "/update-combination-from-listing"], ["variable", "/", "[^/]++", "productId"], ["text", "/sell/catalog/products-v2/combinations"]], "defaults": [], "requirements": {"combinationId": "\\d+"}, "hosttokens": [], "methods": ["PATCH"], "schemes": []}, "admin_products_combinations_edit_combination": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "combinationId"], ["text", "/sell/catalog/products-v2/combinations"]], "defaults": [], "requirements": {"combinationId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_products_combinations_bulk_edit_combination": {"tokens": [["text", "/combinations/bulk-edit"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["PATCH"], "schemes": []}, "admin_products_combinations_delete_combination": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/delete"], ["variable", "/", "\\d+", "combinationId"], ["text", "/sell/catalog/products-v2/combinations"]], "defaults": {"shopId": null}, "requirements": {"combinationId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["DELETE"], "schemes": []}, "admin_products_combinations_bulk_delete": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/combinations/bulk-delete"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": {"shopId": null}, "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_attribute_groups": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/attribute-groups"], ["variable", "/", "[^/]++", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": {"shopId": null}, "requirements": {"shopId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_all_attribute_groups": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/sell/catalog/products-v2/all-attribute-groups"]], "defaults": {"shopId": null}, "requirements": {"shopId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_combinations_generate": {"tokens": [["variable", "/", "\\d+", "shopId"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2/generate-combinations"]], "defaults": {"shopId": null}, "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_images_for_shop": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/images-for-shop"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_product_shop_images": {"tokens": [["text", "/shopImages"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_products_add_image": {"tokens": [["text", "/sell/catalog/products-v2/images/add"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_update_image": {"tokens": [["text", "/update"], ["variable", "/", "\\d+", "productImageId"], ["text", "/sell/catalog/products-v2/images"]], "defaults": [], "requirements": {"productImageId": "\\d+"}, "hosttokens": [], "methods": ["PATCH"], "schemes": []}, "admin_products_delete_image": {"tokens": [["text", "/delete"], ["variable", "/", "\\d+", "productImageId"], ["text", "/sell/catalog/products-v2/images"]], "defaults": [], "requirements": {"productImageId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_specific_prices_list": {"tokens": [["text", "/specific-prices/list"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_specific_prices_create": {"tokens": [["text", "/specific-prices/create"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_products_specific_prices_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "specificPriceId"], ["text", "/sell/catalog/products-v2/specific-prices"]], "defaults": [], "requirements": {"specificPriceId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_products_specific_prices_delete": {"tokens": [["text", "/delete"], ["variable", "/", "\\d+", "specificPriceId"], ["text", "/sell/catalog/products-v2/specific-prices"]], "defaults": [], "requirements": {"specificPriceId": "\\d+"}, "hosttokens": [], "methods": ["DELETE"], "schemes": []}, "admin_products_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST", "PATCH"], "schemes": []}, "admin_products_select_shops": {"tokens": [["text", "/shops"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST", "PATCH"], "schemes": []}, "admin_products_bulk_enable_all_shops": {"tokens": [["text", "/sell/catalog/products-v2/bulk-enable-all-shops"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_enable_shop": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/sell/catalog/products-v2/bulk-enable-shop"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_enable_shop_group": {"tokens": [["variable", "/", "\\d+", "shopGroupId"], ["text", "/sell/catalog/products-v2/bulk-enable-shop-group"]], "defaults": [], "requirements": {"productId": "\\d+", "shopGroupId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_disable_all_shops": {"tokens": [["text", "/sell/catalog/products-v2/bulk-disable-for-all-shops"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_disable_shop": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/sell/catalog/products-v2/bulk-disable-shop"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_disable_shop_group": {"tokens": [["variable", "/", "\\d+", "shopGroupId"], ["text", "/sell/catalog/products-v2/bulk-disable-shop-group"]], "defaults": [], "requirements": {"productId": "\\d+", "shopGroupId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_duplicate_all_shops": {"tokens": [["text", "/sell/catalog/products-v2/bulk-duplicate-all-shops"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_duplicate_shop": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/sell/catalog/products-v2/bulk-duplicate-shop"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_duplicate_shop_group": {"tokens": [["variable", "/", "\\d+", "shopGroupId"], ["text", "/sell/catalog/products-v2/bulk-duplicate-shop-group"]], "defaults": [], "requirements": {"productId": "\\d+", "shopGroupId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_products_bulk_delete_from_all_shops": {"tokens": [["text", "/sell/catalog/products-v2/bulk-delete-from-all-shops"]], "defaults": [], "requirements": {"productId": "\\d+"}, "hosttokens": [], "methods": ["POST", "DELETE"], "schemes": []}, "admin_products_bulk_delete_from_shop": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/sell/catalog/products-v2/bulk-delete-from-shop"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["POST", "DELETE"], "schemes": []}, "admin_products_bulk_delete_from_shop_group": {"tokens": [["variable", "/", "\\d+", "shopGroupId"], ["text", "/sell/catalog/products-v2/bulk-delete-from-shop-group"]], "defaults": [], "requirements": {"productId": "\\d+", "shopGroupId": "\\d+"}, "hosttokens": [], "methods": ["POST", "DELETE"], "schemes": []}, "admin_products_search_product_combinations": {"tokens": [["variable", "/", "\\d+", "languageId"], ["variable", "/", "\\d+", "shopId"], ["text", "/search-product-combinations"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": {"languageId": null, "shopId": null}, "requirements": {"productId": "\\d+", "shopId": "\\d+", "languageId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_products_quantity": {"tokens": [["variable", "/", "\\d+", "shopId"], ["text", "/quantity"], ["variable", "/", "\\d+", "productId"], ["text", "/sell/catalog/products-v2"]], "defaults": [], "requirements": {"productId": "\\d+", "shopId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_categories_get_categories_tree": {"tokens": [["text", "/sell/catalog/categories/tree"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_catalog_price_rules_list_for_product": {"tokens": [["variable", "/", "[^/]++", "productId"], ["text", "/sell/catalog/catalog-price-rules/list-for-product"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_cart_rules_search": {"tokens": [["text", "/sell/catalog/cart-rules/search"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_customers_view": {"tokens": [["text", "/view"], ["variable", "/", "\\d+", "customerId"], ["text", "/sell/customers"]], "defaults": [], "requirements": {"customerId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_customers_search": {"tokens": [["text", "/sell/customers/search"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_customers_carts": {"tokens": [["text", "/carts"], ["variable", "/", "\\d+", "customerId"], ["text", "/sell/customers"]], "defaults": [], "requirements": {"customerId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_customers_orders": {"tokens": [["text", "/orders"], ["variable", "/", "\\d+", "customerId"], ["text", "/sell/customers"]], "defaults": [], "requirements": {"customerId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_addresses_create": {"tokens": [["text", "/sell/addresses/new"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_addresses_edit": {"tokens": [["text", "/edit"], ["variable", "/", "\\d+", "addressId"], ["text", "/sell/addresses"]], "defaults": [], "requirements": {"addressId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_order_addresses_edit": {"tokens": [["text", "/edit"], ["variable", "/", "delivery|invoice", "addressType"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/addresses/order"]], "defaults": [], "requirements": {"orderId": "\\d+", "addressType": "delivery|invoice"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_cart_addresses_edit": {"tokens": [["text", "/edit"], ["variable", "/", "delivery|invoice", "addressType"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/addresses/cart"]], "defaults": [], "requirements": {"cartId": "\\d+", "addressType": "delivery|invoice"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_customer_threads_view": {"tokens": [["text", "/view"], ["variable", "/", "\\d+", "customerThreadId"], ["text", "/sell/customer-service/customer-threads"]], "defaults": [], "requirements": {"customerThreadId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_carts_view": {"tokens": [["text", "/view"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_carts_info": {"tokens": [["text", "/info"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_carts_create": {"tokens": [["text", "/sell/orders/carts/new"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_addresses": {"tokens": [["text", "/addresses"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_carrier": {"tokens": [["text", "/carrier"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_currency": {"tokens": [["text", "/currency"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_language": {"tokens": [["text", "/language"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_set_delivery_settings": {"tokens": [["text", "/rules/delivery-settings"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_add_cart_rule": {"tokens": [["text", "/cart-rules"], ["variable", "/", "[^/]++", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_delete_cart_rule": {"tokens": [["text", "/delete"], ["variable", "/", "[^/]++", "cartRuleId"], ["text", "/cart-rules"], ["variable", "/", "[^/]++", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_add_product": {"tokens": [["text", "/products"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_product_price": {"tokens": [["text", "/price"], ["variable", "/", "\\d+", "productId"], ["text", "/products"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+", "productId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_edit_product_quantity": {"tokens": [["text", "/quantity"], ["variable", "/", "\\d+", "productId"], ["text", "/products"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+", "productId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_carts_delete_product": {"tokens": [["text", "/delete-product"], ["variable", "/", "\\d+", "cartId"], ["text", "/sell/orders/carts"]], "defaults": [], "requirements": {"cartId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_place": {"tokens": [["text", "/sell/orders/place"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_view": {"tokens": [["text", "/view"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET", "POST"], "schemes": []}, "admin_orders_duplicate_cart": {"tokens": [["text", "/duplicate-cart"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_update_product": {"tokens": [["variable", "/", "\\d+", "orderDetailId"], ["text", "/products"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+", "orderDetailId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_partial_refund": {"tokens": [["text", "/partial-refund"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_standard_refund": {"tokens": [["text", "/standard-refund"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_return_product": {"tokens": [["text", "/return-product"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_send_process_order_email": {"tokens": [["text", "/sell/orders/process-order-email"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_add_product": {"tokens": [["text", "/products"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_delete_product": {"tokens": [["text", "/delete"], ["variable", "/", "\\d+", "orderDetailId"], ["text", "/products"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+", "orderDetailId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_get_discounts": {"tokens": [["text", "/discounts"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_prices": {"tokens": [["text", "/prices"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_payments": {"tokens": [["text", "/payments"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_products": {"tokens": [["text", "/products"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_invoices": {"tokens": [["text", "/invoices"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_documents": {"tokens": [["text", "/documents"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_get_shipping": {"tokens": [["text", "/shipping"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_cancellation": {"tokens": [["text", "/cancellation"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_configure_product_pagination": {"tokens": [["text", "/sell/orders/configure-product-pagination"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["POST"], "schemes": []}, "admin_orders_product_prices": {"tokens": [["text", "/products/prices"], ["variable", "/", "\\d+", "orderId"], ["text", "/sell/orders"]], "defaults": [], "requirements": {"orderId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_orders_products_search": {"tokens": [["text", "/sell/orders/products/search"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_attachments_attachment_info": {"tokens": [["text", "/info"], ["variable", "/", "\\d+", "attachmentId"], ["text", "/sell/attachments"]], "defaults": [], "requirements": {"attachmentId": "\\d+"}, "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_attachments_search": {"tokens": [["variable", "/", "[^/]++", "searchPhrase"], ["text", "/sell/attachments/search"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}, "admin_shops_search": {"tokens": [["variable", "/", "[^/]++", "searchTerm"], ["text", "/configure/advanced/shops/search"]], "defaults": [], "requirements": [], "hosttokens": [], "methods": ["GET"], "schemes": []}}, "prefix": "", "host": "localhost", "port": "", "scheme": "http", "locale": ""}