{**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *}
{if isset($php_errors) && $php_errors|@count}
<div class="bootstrap">
	<div id="error-modal" class="modal fade">
		<div class="modal-dialog">
			<div class="alert alert-danger clearfix">
				{foreach $php_errors item=php_error}
					{l s='%1$s on line %2$s in file %3$s' sprintf=[$php_error.type|escape, $php_error.errline|escape, $php_error.errfile|escape] d='Admin.Notifications.Error'}<br />
					[{$php_error.errno|escape}] {$php_error.errstr|escape}<br /><br />
				{/foreach}
				<button type="button" class="btn btn-default float-right" data-dismiss="modal"><i class="icon-remove"></i> {l s='Close' d='Admin.Actions'}</button>
			</div>
		</div>
	</div>
</div>
{/if}
