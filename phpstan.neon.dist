parameters:
  level: 5
  bootstrapFiles:
    - .github/workflows/phpstan/autoload.php
  paths:
    - admin-dev
    - app
    - bin
    - cache
    - classes
    - config
    - controllers
    - docs
    - download
    - img
    - install-dev
    - js
    - localization
    - mails
    # - modules
    - override
    - pdf
    - src
    - tests
    - themes
    - tools
    - translations
    - upload
    - webservice
  excludePaths:
    - admin-dev/filemanager
    - admin-dev/themes
    - classes/lang/KeysReference
    - install-dev/theme/views
    - install-dev/theme/config.php
    - tests/Resources
    - tests/Unit/Core/Foundation/IoC/Fixtures
    - */node_modules/*
  ignoreErrors:
    - '#^Call to an undefined method Symfony\\Component\\Config\\Definition\\Builder\\NodeDefinition\:\:#'
    - '#^Unsafe usage of new static\(\)\.$#'
    - '#constructor expects Symfony\\Contracts\\Translation\\TranslatorInterface\|null, Symfony\\Component\\Translation\\TranslatorInterface given\.$#'
    - '#^Method Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface\:\:dispatch\(\) invoked with 2 parameters, 1 required\.$#'
    - '~^Access to an undefined property PrestaShop\\PrestaShop\\Core\\Module\\ModuleInterface::\$(attributes|disk|database)\.$~'
    ## Smarty functions
    - '#^Function smartyRegisterFunction not found\.$#'
    ## Doctrine Entities
    - '#Property PrestaShopBundle\\Entity\\[A-Za-z]+\:\:\$[A-Za-z]+ is never written, only read.#'
    - message: "#^Dead catch \\- Symfony\\\\Component\\\\HttpFoundation\\\\File\\\\Exception\\\\FileNotFoundException is never thrown in the try block\\.$#"
      count: 1
      path: src/PrestaShopBundle/Controller/Admin/SecuredFileReaderController.php
  reportUnmatchedIgnoredErrors: false
  universalObjectCratesClasses:
    - Cookie
    - ObjectModel
    - Order
    - OrderInvoice
    - Product
  dynamicConstantNames:
    - _DB_PREFIX_
    - _PS_CREATION_DATE_
    - _PS_DEBUG_PROFILING_
    - _PS_DEBUG_SQL_
    - _PS_DISPLAY_COMPATIBILITY_WARNING_
    - _PS_DO_NOT_LOAD_CONFIGURATION_
    - _PS_IN_TEST_
    - _PS_INSTALL_MINIMUM_PHP_VERSION_ID_
    - _PS_INSTALL_MAXIMUM_PHP_VERSION_ID_
    - _PS_INSTALL_VERSION_
    - _PS_MODE_DEMO_
    - _PS_MODE_DEV_
    - _PS_VERSION_
    - __PS_BASE_URI__
    - PHP_VERSION_ID
    - Memcached::HAVE_IGBINARY
    - PaymentModuleCore::DEBUG_MODE
    - _PS_API_IN_USE_
    - _PS_ALLOW_MULTI_STATEMENTS_QUERIES_
  disallowedNamespaces:
        -
            class: 'Symfony\Component\Translation\TranslatorInterface'
            message: 'use Symfony\Contracts\Translation\TranslatorInterface instead'
            disallowIn:
                - src/*
  disallowedMethodCalls:
        -
            method: 'PrestaShopBundle\Form\Admin\Type\CommonAbstractType::getConfiguration()'
            message: 'Use dependency injection instead'
            disallowIn:
                - src/*

