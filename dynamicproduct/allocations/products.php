<?php

use DynamicProduct\classes\models\DynamicInputField;

/** @var int $id_product */
/** @var DynamicInputField[] $input_fields */
/** @var array $file */

/**
 * @param string $pdf
 * @return int
 */
if (!function_exists('getPagesCount')) {
    function getPagesCount($pdf)
    {
        // read pdf file line by line
        $count = 0;
        $f = fopen($pdf, "r");
        if ($f) {
            while (($line = fgets($f)) !== false) {
                $rs = preg_match_all("/\/Count (\d+)/", $line, $m);
                if ($rs && is_array($m) && isset($m[1]) && is_array($m[1])) {
                    foreach ($m[1] as $new_count) {
                        $new_count = (int)$new_count;
                        if ($new_count > $count) {
                            $count = $new_count;
                        }
                    }
                }
            }
        }

        return $count;
    }
}

if (isset($file)) {
    $file_input_field = $input_fields['file'];
    if ($file_input_field->data && isset($file_input_field->data[0])) {
        $file = $file_input_field->data[0];
        $path = $file_input_field->getFilePath($file_input_field->data[0]['file']);
        if (is_file($path)) {
            $page_copy = getPagesCount($path);
        }
    }
}

if (isset($reverse_discount)) {
    $reverse_discount = 1;
    $group_reduction = (float)\Group::getReduction(Context::getContext()->customer->id);
    if ($group_reduction > 0) {
        $reverse_discount = 1 / (1 - $group_reduction / 100);
    }
}

if (isset($customer_group)){
    $context = Context::getContext();
    $default_lang = Configuration::get('PS_LANG_DEFAULT');
    $id_customer_group = (int)$context->customer->id_default_group;
    if($id_customer_group){
        $group = new Group($id_customer_group, $default_lang);
        $customer_group = $group->name; // Will assign Visitor or
    }
}

if (isset($tax_rate)){
    // first read the tax rate of the current product
    /** @var $module DynamicProduct */
    $tax_rate = $module->calculator->getTax($id_product);
}
