<?php

require dirname(__FILE__) . '/config/config.inc.php';

// please backup your DB before running this script.
// if it gets interrupted, you may have to restore from backup.

$id_manufacturer = 1;
$price_increase_coefficient = 1.1;
$update_combinations = true; // can be slow if there are a lot of combinations

$id_default_lang = Configuration::get('PS_LANG_DEFAULT');

$affected_combinations = 0;
if ($update_combinations) {
    Db::getInstance()->execute('
        UPDATE ' . _DB_PREFIX_ . 'product_attribute
        SET price = price * ' . (float)$price_increase_coefficient . '
        WHERE id_product IN (
            SELECT id_product FROM ' . _DB_PREFIX_ . 'product
            WHERE id_manufacturer = ' . (int)$id_manufacturer . '
        )
    ');
    $affected_combinations = Db::getInstance()->Affected_Rows();
}

// update product prices, use id_manufacturer
Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'product
    SET price = price * ' . (float)$price_increase_coefficient . '
    WHERE id_manufacturer = ' . (int)$id_manufacturer . '
');

$affected_products = Db::getInstance()->Affected_Rows();
