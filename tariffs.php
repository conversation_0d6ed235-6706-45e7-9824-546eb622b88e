<?php

require dirname(__FILE__) . '/config/config.inc.php';

$cookie = new <PERSON><PERSON>('psAdmin');
if (!$cookie->id_employee) {
    die('Please login to the backoffice first!');
}

$manufacturers = Manufacturer::getManufacturers();

if (!isset($_POST['submit'])) {
    exit("
        <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/water.css@2/out/water.css'>
        <fieldset>
            <legend>Update prices</legend>
            
            <form method='post'>
                <!-- add a select box to select the manufacturer -->
                <div>
                    <select name='id_manufacturer'>
                        " . implode('', array_map(function ($manufacturer) {
            return "<option value='{$manufacturer['id_manufacturer']}'>{$manufacturer['name']}</option>";
        }, $manufacturers)) . "
                    </select>
                </div>

                <div>
                    <label>
                        Price increase coefficient:
                        <input type='number' name='price_increase_coefficient' value='1.1' step='0.1' min='0.1' required>
                    </label>
                </div>

                <div>
                    <label>
                        <input type='checkbox' name='update_combinations' value='1' checked> 
                        Update combinations (can be slow if there are a lot of combinations)
                    </label>
                </div>

                <div>
                    <input type='submit' name='submit' value='Update prices' />
                </div>
            </form>
        </fieldset>
    ");
}

// please backup your DB before running this script.
// if it gets interrupted, you may have to restore from backup.

$id_manufacturer = (int)$_POST['id_manufacturer'];
$price_increase_coefficient = (float)$_POST['price_increase_coefficient'];
$update_combinations = isset($_POST['update_combinations']); // can be slow if there are a lot of combinations

$id_default_lang = Configuration::get('PS_LANG_DEFAULT');

$affected_combinations = 0;
if ($update_combinations) {
    // update combination prices
    Db::getInstance()->execute('
        UPDATE ' . _DB_PREFIX_ . 'product_attribute pa
        INNER JOIN ' . _DB_PREFIX_ . 'product p ON pa.id_product = p.id_product
        SET pa.price = pa.price * ' . (float)$price_increase_coefficient . '
        WHERE p.id_manufacturer = ' . (int)$id_manufacturer . '
    ');
    $affected_combinations = Db::getInstance()->Affected_Rows();

    Db::getInstance()->execute('
        UPDATE ' . _DB_PREFIX_ . 'product_attribute_shop pa
        INNER JOIN ' . _DB_PREFIX_ . 'product p ON pa.id_product = p.id_product
        SET pa.price = pa.price * ' . (float)$price_increase_coefficient . '
        WHERE p.id_manufacturer = ' . (int)$id_manufacturer . '
    ');

    $affected_combinations += Db::getInstance()->Affected_Rows();
}

// update product prices
Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'product
    SET price = price * ' . (float)$price_increase_coefficient . '
    WHERE id_manufacturer = ' . (int)$id_manufacturer . '
');

$affected_products = Db::getInstance()->Affected_Rows();

Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'product_shop ps
    INNER JOIN ' . _DB_PREFIX_ . 'product p ON ps.id_product = p.id_product
    SET ps.price = ps.price * ' . (float)$price_increase_coefficient . '
    WHERE p.id_manufacturer = ' . (int)$id_manufacturer . '
');

$affected_products += Db::getInstance()->Affected_Rows();

// update dynamic field options
Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'dynamicproduct_radio_option ro
    INNER JOIN ' . _DB_PREFIX_ . 'dynamicproduct_field f ON ro.id_field = f.id_field
    INNER JOIN ' . _DB_PREFIX_ . 'product p ON f.id_product = p.id_product
    SET ro.value = ro.value * ' . (float)$price_increase_coefficient . '
    WHERE p.id_manufacturer = ' . (int)$id_manufacturer . ' AND ro.value > 1
');
$affected_radio_options = Db::getInstance()->Affected_Rows();

Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'dynamicproduct_dropdown_option do
    INNER JOIN ' . _DB_PREFIX_ . 'dynamicproduct_field f ON do.id_field = f.id_field
    INNER JOIN ' . _DB_PREFIX_ . 'product p ON f.id_product = p.id_product
    SET do.value = do.value * ' . (float)$price_increase_coefficient . '
    WHERE p.id_manufacturer = ' . (int)$id_manufacturer . ' AND do.value > 1
');
$affected_dropdown_options = Db::getInstance()->Affected_Rows();

Db::getInstance()->execute('
    UPDATE ' . _DB_PREFIX_ . 'dynamicproduct_thumbnails_option tho
    INNER JOIN ' . _DB_PREFIX_ . 'dynamicproduct_field f ON tho.id_field = f.id_field
    INNER JOIN ' . _DB_PREFIX_ . 'product p ON f.id_product = p.id_product
    SET tho.value = tho.value * ' . (float)$price_increase_coefficient . '
    WHERE p.id_manufacturer = ' . (int)$id_manufacturer . ' AND tho.value > 1
');
$affected_thumbnails_options = Db::getInstance()->Affected_Rows();

echo "<pre>\n";
echo "┌─────────────────────────────────┬─────────┐\n";
echo "│ Item Type                       │ Count   │\n";
echo "├─────────────────────────────────┼─────────┤\n";
echo "│ Products                        │ " . str_pad($affected_products, 7) . " │\n";
echo "│ Combinations                    │ " . str_pad($affected_combinations, 7) . " │\n";
echo "│ Radio Options                   │ " . str_pad($affected_radio_options, 7) . " │\n";
echo "│ Dropdown Options                │ " . str_pad($affected_dropdown_options, 7) . " │\n";
echo "│ Thumbnails Options              │ " . str_pad($affected_thumbnails_options, 7) . " │\n";
echo "└─────────────────────────────────┴─────────┘\n";
echo "Price coefficient: " . $price_increase_coefficient . " | Manufacturer ID: " . $id_manufacturer . "\n";
echo "</pre>\n";

